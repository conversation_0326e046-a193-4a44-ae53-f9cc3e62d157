# Projet RAG Confluence

Ce projet contient un système de Retrieval Augmented Generation (RAG) pour Confluence, organisé autour du module Python `confluence_rag`.

## Structure du projet

```
/rag/
├── README.md                    # Ce fichier - Documentation du projet global
├── requirements.txt             # Dépendances Python du projet
├── main.py                      # Point d'entrée principal
├── run_sync.py                  # Script de lancement de la synchronisation
├── .env.example                 # Fichier d'exemple de configuration
├── criteres_recherche.json      # Exemple de critères de recherche
├── demo_log_security.py         # Script de démonstration de sécurité
├── monitoring_app.py            # Application de monitoring FastAPI
└── confluence_rag/              # Module principal du système RAG
    ├── README.md                # Documentation complète du module
    ├── CHANGELOG.md             # Historique des modifications
    ├── __init__.py              # Package Python
    ├── [modules Python]         # Modules fonctionnels
    ├── docs/                    # Documentation détaillée
    ├── tests/                   # Tests unitaires et d'intégration
    ├── examples/                # Exemples d'utilisation
    ├── benchmarks/              # Scripts de benchmark
    └── scripts/                 # Scripts utilitaires
```

## Installation rapide

```bash
# Cloner le projet
git clone <repository-url>
cd rag

# Créer un environnement virtuel
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# ou .venv\Scripts\activate  # Windows

# Installer les dépendances
pip install -r requirements.txt
```

## Configuration

1. Copiez le fichier de configuration d'exemple :
```bash
cp .env.example .env
```

2. Éditez le fichier `.env` avec vos paramètres Confluence :
```bash
# Configuration Confluence
CONFLUENCE_BASE_URL=https://your-confluence.atlassian.net
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_TOKEN=your-pat-token

# Configuration de stockage
STORAGE_TYPE=filesystem
OUTPUT_DIR=output_data_dir
```

3. Personnalisez les critères de recherche dans `criteres_recherche.json`

## Utilisation

### Synchronisation simple
```bash
python run_sync.py
```

### Utilisation du module Python
```python
from confluence_rag import ConfluenceClient, ConfluenceConfig, SearchCriteria

# Configuration
config = ConfluenceConfig.from_env()
criteria = SearchCriteria(spaces=["SPACE1"], max_results=100)

# Utilisation
async with ConfluenceClient(config) as client:
    results = await client.search_content(criteria)
```

### Monitoring (optionnel)
```bash
python monitoring_app.py
# Accédez à http://localhost:8000/docs
```

## Documentation

Pour la documentation complète du système RAG Confluence, consultez :

📖 **[confluence_rag/README.md](confluence_rag/README.md)** - Documentation complète du module

Cette documentation inclut :
- Guide d'installation détaillé
- Configuration avancée
- Exemples d'utilisation
- Architecture du système
- Optimisations de performance
- Sécurité et bonnes pratiques
- API de référence

## Fonctionnalités principales

- 🔐 **Authentification sécurisée** avec Personal Access Tokens (PAT)
- 📊 **Récupération intelligente** de contenu Confluence (pages, blogs, pièces jointes)
- 🚀 **Optimisations de performance** (pagination parallèle, sessions réutilisables)
- 💾 **Stockage flexible** (système de fichiers local ou Google Cloud Storage)
- 🔍 **Filtrage avancé** par espaces, étiquettes, types de contenu
- 📈 **Suivi des changements** pour synchronisations incrémentales
- 🛡️ **Sécurité des logs** avec masquage automatique des informations sensibles
- 🔧 **Architecture modulaire** et extensible

## Support et contribution

- **Issues** : Signalez les problèmes via les issues GitHub
- **Documentation** : Consultez `confluence_rag/docs/` pour la documentation technique
- **Tests** : Exécutez `python -m pytest confluence_rag/tests/` pour les tests
- **Exemples** : Voir `confluence_rag/examples/` pour des exemples d'utilisation

## Licence

[Spécifiez votre licence ici]

---

**Note** : Ce README présente une vue d'ensemble du projet. Pour la documentation technique complète, consultez [confluence_rag/README.md](confluence_rag/README.md).
