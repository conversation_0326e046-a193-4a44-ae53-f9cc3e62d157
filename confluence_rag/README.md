# Module Confluence RAG

Module Python pour la récupération et le traitement de contenu Confluence dans un système RAG (Retrieval Augmented Generation).

## Vue d'ensemble

Ce module fournit une interface complète pour :
- Se connecter à Confluence via l'API REST
- Récupérer du contenu selon des critères spécifiques
- Traiter les pages et pièces jointes
- Stocker les données sur système de fichiers ou Google Cloud Storage
- Optimiser les performances avec des pools de threads

## Architecture

### Modules principaux

- **`client.py`** - Client Confluence avec optimisations de performance
- **`config.py`** - Configuration et modèles de données
- **`processing.py`** - Traitement du contenu et des pièces jointes
- **`storage.py`** - Stockage sur filesystem ou GCS
- **`orchestrator.py`** - Orchestration du processus de synchronisation
- **`thread_pool_manager.py`** - Gestion optimisée des pools de threads
- **`tracking.py`** - Suivi des changements
- **`logging_utils.py`** - Journalisation structurée et sécurisée

### Modules utilitaires

- **`circuit_breaker.py`** - Pattern Circuit Breaker pour la résilience
- **`exceptions.py`** - Exceptions personnalisées
- **`utils.py`** - Utilitaires divers
- **`models.py`** - Modèles de données Pydantic

## Installation

```bash
# Installer les dépendances
pip install -r ../requirements.txt

# Importer le module
from confluence_rag import ConfluenceClient, ConfluenceConfig
```

## Utilisation rapide

```python
from confluence_rag import ConfluenceClient, ConfluenceConfig, SearchCriteria

# Configuration
config = ConfluenceConfig(
    base_url="https://your-confluence.atlassian.net",
    username="<EMAIL>",
    token="your-pat-token"
)

criteria = SearchCriteria(
    spaces=["SPACE1", "SPACE2"],
    content_types=["page"],
    max_results=100
)

# Utilisation
async with ConfluenceClient(config) as client:
    results = await client.search_content(criteria)
    for page in results:
        print(f"Page: {page.title}")
```

## Fonctionnalités avancées

### Optimisations de performance
- Pagination parallèle pour les grandes requêtes
- Pools de threads spécialisés (I/O, traitement, API)
- Gestion intelligente des sessions HTTP
- Circuit breaker pour la résilience

### Sécurité
- Filtrage automatique des informations sensibles dans les logs
- Journalisation structurée avec identifiants de corrélation
- Gestion sécurisée des tokens d'authentification

### Stockage flexible
- Support du système de fichiers local
- Support de Google Cloud Storage
- Compression et organisation automatique des données

## Tests

```bash
# Tests unitaires
python -m pytest tests/ -v

# Tests de performance
python tests/test_parallel_pagination.py
python tests/test_performance_optimization.py

# Benchmarks
python benchmarks/parallel_pagination_benchmark.py
```

## Documentation

Consultez le répertoire `docs/` pour la documentation détaillée :

- [Guide de pagination parallèle](docs/PARALLEL_PAGINATION_GUIDE.md)
- [Optimisations de performance](docs/PERFORMANCE_OPTIMIZATION.md)
- [Gestion des pools de threads](docs/THREAD_POOL_OPTIMIZATION.md)
- [Journalisation structurée](docs/journalisation_structuree.md)
- [Sécurité des logs](docs/securite_logs.md)
- [Circuit Breaker](docs/circuit_breaker.md)

## Exemples

Voir le répertoire `examples/` pour des exemples d'utilisation :

- [Pagination parallèle](examples/parallel_pagination_example.py)
- [Recherche optimisée](examples/optimized_search_example.py)

## Version

Version actuelle : 1.2.0

## Contribution

Pour contribuer à ce module, veuillez consulter la documentation de développement dans `docs/`.
