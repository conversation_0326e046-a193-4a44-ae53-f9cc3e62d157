#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système RAG Confluence avec optimisations de gestion des threads.
"""

from .config import (
    ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig,
    ThreadPoolConfig, RetryConfig, CircuitBreakerConfig
)
from .client import ConfluenceClient
from .processing import AttachmentProcessor, ContentRetriever
from .storage import FileSystemStorage, GCSStorage, get_storage_provider
from .orchestrator import SyncOrchestrator
from .thread_pool_manager import (
    ThreadPoolManager, get_thread_pool_manager, shutdown_global_thread_pools,
    thread_pool_executor
)

__version__ = "1.2.0"
__author__ = "Confluence RAG Team"
__description__ = "Système RAG Confluence avec optimisations de gestion des threads"

__all__ = [
    # Configuration
    "ConfluenceConfig",
    "SearchCriteria",
    "StorageConfig",
    "ProcessingConfig",
    "ThreadPoolConfig",
    "RetryConfig",
    "CircuitBreakerConfig",

    # Client et traitement
    "ConfluenceClient",
    "AttachmentProcessor",
    "ContentRetriever",

    # Stockage
    "FileSystemStorage",
    "GCSStorage",
    "get_storage_provider",

    # Orchestration
    "SyncOrchestrator",

    # Gestion des threads optimisée
    "ThreadPoolManager",
    "get_thread_pool_manager",
    "shutdown_global_thread_pools",
    "thread_pool_executor",
]