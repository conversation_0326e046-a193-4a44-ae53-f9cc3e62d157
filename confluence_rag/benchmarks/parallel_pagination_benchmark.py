#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Benchmark spécifique pour la pagination parallèle du client Confluence.
"""

import asyncio
import time
import statistics
import logging
from typing import List, Dict, Any
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class ParallelPaginationBenchmark:
    """Classe pour effectuer des benchmarks de pagination parallèle."""

    def __init__(self, config: ConfluenceConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

    async def benchmark_pagination_methods(self, criteria: SearchCriteria, iterations: int = 3) -> Dict[str, Any]:
        """
        Compare les performances entre pagination séquentielle et parallèle.
        
        Args:
            criteria: Critères de recherche
            iterations: Nombre d'itérations pour calculer la moyenne
            
        Returns:
            Statistiques comparatives
        """
        self.logger.info(f"Benchmark de comparaison des méthodes de pagination ({iterations} itérations)")
        
        # Benchmark pagination séquentielle
        sequential_times = []
        sequential_results_count = 0
        
        self.logger.info("🔄 Test de la pagination séquentielle...")
        config_sequential = self.config.model_copy()
        config_sequential.enable_parallel_pagination = False
        
        for i in range(iterations):
            async with ConfluenceClient(config_sequential) as client:
                start_time = time.time()
                results = await client.search_content(criteria)
                end_time = time.time()
                
                execution_time = end_time - start_time
                sequential_times.append(execution_time)
                sequential_results_count = len(results)
                
                self.logger.info(f"  Itération {i+1}: {execution_time:.3f}s, {len(results)} résultats")
                await asyncio.sleep(0.5)  # Pause entre les itérations
        
        # Benchmark pagination parallèle
        parallel_times = []
        parallel_results_count = 0
        
        self.logger.info("🚀 Test de la pagination parallèle...")
        config_parallel = self.config.model_copy()
        config_parallel.enable_parallel_pagination = True
        config_parallel.parallel_pagination_threshold = 50  # Seuil bas pour forcer la parallélisation
        
        for i in range(iterations):
            async with ConfluenceClient(config_parallel) as client:
                start_time = time.time()
                results = await client.search_content(criteria)
                end_time = time.time()
                
                execution_time = end_time - start_time
                parallel_times.append(execution_time)
                parallel_results_count = len(results)
                
                self.logger.info(f"  Itération {i+1}: {execution_time:.3f}s, {len(results)} résultats")
                await asyncio.sleep(0.5)  # Pause entre les itérations
        
        # Calculer les statistiques
        sequential_avg = statistics.mean(sequential_times)
        parallel_avg = statistics.mean(parallel_times)
        
        speedup = sequential_avg / parallel_avg if parallel_avg > 0 else float('inf')
        improvement = ((sequential_avg - parallel_avg) / sequential_avg) * 100 if sequential_avg > 0 else 0
        
        stats = {
            "iterations": iterations,
            "sequential": {
                "avg_time": sequential_avg,
                "min_time": min(sequential_times),
                "max_time": max(sequential_times),
                "std_dev": statistics.stdev(sequential_times) if len(sequential_times) > 1 else 0,
                "results_count": sequential_results_count,
                "times": sequential_times
            },
            "parallel": {
                "avg_time": parallel_avg,
                "min_time": min(parallel_times),
                "max_time": max(parallel_times),
                "std_dev": statistics.stdev(parallel_times) if len(parallel_times) > 1 else 0,
                "results_count": parallel_results_count,
                "times": parallel_times
            },
            "comparison": {
                "speedup_factor": speedup,
                "improvement_percent": improvement,
                "consistent_results": sequential_results_count == parallel_results_count
            }
        }
        
        return stats

    async def benchmark_parallel_configuration(self, criteria: SearchCriteria) -> Dict[str, Any]:
        """
        Teste différentes configurations de pagination parallèle.
        
        Args:
            criteria: Critères de recherche
            
        Returns:
            Statistiques pour chaque configuration
        """
        self.logger.info("Benchmark des configurations de pagination parallèle")
        
        configurations = [
            {"max_parallel_requests": 2, "name": "Conservative (2 parallèles)"},
            {"max_parallel_requests": 3, "name": "Balanced (3 parallèles)"},
            {"max_parallel_requests": 5, "name": "Aggressive (5 parallèles)"},
            {"max_parallel_requests": 8, "name": "Very Aggressive (8 parallèles)"},
        ]
        
        results = {}
        
        for config_params in configurations:
            config_name = config_params["name"]
            self.logger.info(f"🔧 Test de la configuration: {config_name}")
            
            config = self.config.model_copy()
            config.enable_parallel_pagination = True
            config.max_parallel_requests = config_params["max_parallel_requests"]
            config.parallel_pagination_threshold = 50
            
            try:
                async with ConfluenceClient(config) as client:
                    start_time = time.time()
                    search_results = await client.search_content(criteria)
                    end_time = time.time()
                    
                    execution_time = end_time - start_time
                    
                    results[config_name] = {
                        "execution_time": execution_time,
                        "results_count": len(search_results),
                        "max_parallel_requests": config_params["max_parallel_requests"],
                        "success": True
                    }
                    
                    self.logger.info(f"  ✅ {execution_time:.3f}s, {len(search_results)} résultats")
                    
            except Exception as e:
                results[config_name] = {
                    "execution_time": None,
                    "results_count": 0,
                    "max_parallel_requests": config_params["max_parallel_requests"],
                    "success": False,
                    "error": str(e)
                }
                self.logger.error(f"  ❌ Erreur: {e}")
            
            await asyncio.sleep(1)  # Pause entre les configurations
        
        return results

    async def benchmark_scalability(self, base_criteria: SearchCriteria) -> Dict[str, Any]:
        """
        Teste la scalabilité de la pagination parallèle avec différentes tailles de requêtes.
        
        Args:
            base_criteria: Critères de base à modifier
            
        Returns:
            Statistiques de scalabilité
        """
        self.logger.info("Benchmark de scalabilité de la pagination parallèle")
        
        test_sizes = [50, 100, 200, 500, 1000]
        results = {}
        
        config = self.config.model_copy()
        config.enable_parallel_pagination = True
        config.max_parallel_requests = 3
        config.parallel_pagination_threshold = 50
        
        for size in test_sizes:
            self.logger.info(f"📊 Test avec {size} résultats maximum")
            
            criteria = base_criteria.model_copy()
            criteria.max_results = size
            
            try:
                async with ConfluenceClient(config) as client:
                    start_time = time.time()
                    search_results = await client.search_content(criteria)
                    end_time = time.time()
                    
                    execution_time = end_time - start_time
                    throughput = len(search_results) / execution_time if execution_time > 0 else 0
                    
                    results[f"{size}_results"] = {
                        "requested": size,
                        "obtained": len(search_results),
                        "execution_time": execution_time,
                        "throughput": throughput,  # résultats par seconde
                        "success": True
                    }
                    
                    self.logger.info(f"  ✅ {execution_time:.3f}s, {len(search_results)} résultats, {throughput:.1f} résultats/s")
                    
            except Exception as e:
                results[f"{size}_results"] = {
                    "requested": size,
                    "obtained": 0,
                    "execution_time": None,
                    "throughput": 0,
                    "success": False,
                    "error": str(e)
                }
                self.logger.error(f"  ❌ Erreur: {e}")
            
            await asyncio.sleep(1)  # Pause entre les tests
        
        return results

    def print_comparison_results(self, stats: Dict[str, Any]):
        """Affiche les résultats de comparaison de manière formatée."""
        print(f"\n{'='*70}")
        print("COMPARAISON PAGINATION SÉQUENTIELLE vs PARALLÈLE")
        print(f"{'='*70}")
        
        seq = stats["sequential"]
        par = stats["parallel"]
        comp = stats["comparison"]
        
        print(f"Itérations: {stats['iterations']}")
        print(f"\n📊 PAGINATION SÉQUENTIELLE:")
        print(f"  Temps moyen: {seq['avg_time']:.3f}s")
        print(f"  Temps min/max: {seq['min_time']:.3f}s / {seq['max_time']:.3f}s")
        print(f"  Écart-type: {seq['std_dev']:.3f}s")
        print(f"  Résultats: {seq['results_count']}")
        
        print(f"\n🚀 PAGINATION PARALLÈLE:")
        print(f"  Temps moyen: {par['avg_time']:.3f}s")
        print(f"  Temps min/max: {par['min_time']:.3f}s / {par['max_time']:.3f}s")
        print(f"  Écart-type: {par['std_dev']:.3f}s")
        print(f"  Résultats: {par['results_count']}")
        
        print(f"\n📈 COMPARAISON:")
        print(f"  Facteur d'accélération: {comp['speedup_factor']:.2f}x")
        print(f"  Amélioration: {comp['improvement_percent']:.1f}%")
        print(f"  Résultats cohérents: {'✅' if comp['consistent_results'] else '❌'}")

    def print_configuration_results(self, results: Dict[str, Any]):
        """Affiche les résultats de configuration de manière formatée."""
        print(f"\n{'='*70}")
        print("BENCHMARK DES CONFIGURATIONS PARALLÈLES")
        print(f"{'='*70}")
        
        for config_name, data in results.items():
            print(f"\n🔧 {config_name}:")
            if data["success"]:
                print(f"  Temps d'exécution: {data['execution_time']:.3f}s")
                print(f"  Résultats: {data['results_count']}")
                print(f"  Requêtes parallèles: {data['max_parallel_requests']}")
            else:
                print(f"  ❌ Échec: {data.get('error', 'Erreur inconnue')}")

async def main():
    """Fonction principale exécutant tous les benchmarks."""
    logger = logging.getLogger(__name__)
    
    try:
        # Configuration
        config = ConfluenceConfig.from_env()
        benchmark = ParallelPaginationBenchmark(config)
        
        # Critères de test
        criteria = SearchCriteria(
            spaces=["EXAMPLE"],  # Remplacez par un espace avec beaucoup de contenu
            types=["page"],
            max_results=200
        )
        
        # Benchmark 1: Comparaison des méthodes
        logger.info("🚀 Démarrage du benchmark de comparaison")
        comparison_stats = await benchmark.benchmark_pagination_methods(criteria, iterations=2)
        benchmark.print_comparison_results(comparison_stats)
        
        print("\n" + "="*70 + "\n")
        
        # Benchmark 2: Configurations parallèles
        logger.info("🔧 Démarrage du benchmark des configurations")
        config_results = await benchmark.benchmark_parallel_configuration(criteria)
        benchmark.print_configuration_results(config_results)
        
        print("\n" + "="*70 + "\n")
        
        # Benchmark 3: Scalabilité
        logger.info("📊 Démarrage du benchmark de scalabilité")
        scalability_results = await benchmark.benchmark_scalability(criteria)
        
        print("BENCHMARK DE SCALABILITÉ")
        print("="*70)
        for test_name, data in scalability_results.items():
            if data["success"]:
                print(f"{test_name}: {data['execution_time']:.3f}s, {data['throughput']:.1f} résultats/s")
            else:
                print(f"{test_name}: ❌ Échec")
        
        logger.info("✅ Tous les benchmarks de pagination parallèle terminés!")
        
    except Exception as e:
        logger.error(f"❌ Erreur lors du benchmark: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
