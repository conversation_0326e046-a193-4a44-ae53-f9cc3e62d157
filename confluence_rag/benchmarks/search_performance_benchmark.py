#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Benchmark de performance pour comparer l'ancienne et la nouvelle approche de search_content().
"""

import asyncio
import time
import statistics
import logging
from typing import List, Dict, Any
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SearchPerformanceBenchmark:
    """Classe pour effectuer des benchmarks de performance des recherches."""

    def __init__(self, config: ConfluenceConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

    async def benchmark_single_search(self, criteria: SearchCriteria, iterations: int = 5) -> Dict[str, Any]:
        """
        Benchmark d'une recherche unique répétée plusieurs fois.
        
        Args:
            criteria: Critères de recherche
            iterations: Nombre d'itérations pour calculer la moyenne
            
        Returns:
            Statistiques de performance
        """
        self.logger.info(f"Début du benchmark de recherche unique ({iterations} itérations)")
        
        execution_times = []
        result_counts = []
        
        async with ConfluenceClient(self.config) as client:
            for i in range(iterations):
                self.logger.info(f"Itération {i+1}/{iterations}")
                
                start_time = time.time()
                results = await client.search_content(criteria)
                end_time = time.time()
                
                execution_time = end_time - start_time
                execution_times.append(execution_time)
                result_counts.append(len(results))
                
                self.logger.info(f"  Temps: {execution_time:.3f}s, Résultats: {len(results)}")
                
                # Petite pause entre les itérations pour éviter la surcharge
                await asyncio.sleep(0.5)
        
        # Calculer les statistiques
        stats = {
            "iterations": iterations,
            "avg_time": statistics.mean(execution_times),
            "min_time": min(execution_times),
            "max_time": max(execution_times),
            "std_dev": statistics.stdev(execution_times) if len(execution_times) > 1 else 0,
            "avg_results": statistics.mean(result_counts),
            "total_time": sum(execution_times),
            "times": execution_times
        }
        
        return stats

    async def benchmark_multiple_searches(self, criteria_list: List[SearchCriteria]) -> Dict[str, Any]:
        """
        Benchmark de recherches multiples avec session réutilisable.
        
        Args:
            criteria_list: Liste des critères de recherche
            
        Returns:
            Statistiques de performance
        """
        self.logger.info(f"Début du benchmark de recherches multiples ({len(criteria_list)} recherches)")
        
        start_time = time.time()
        all_results = []
        search_times = []
        
        async with ConfluenceClient(self.config) as client:
            for i, criteria in enumerate(criteria_list):
                self.logger.info(f"Recherche {i+1}/{len(criteria_list)}")
                
                search_start = time.time()
                results = await client.search_content(criteria)
                search_end = time.time()
                
                search_time = search_end - search_start
                search_times.append(search_time)
                all_results.extend(results)
                
                self.logger.info(f"  Temps: {search_time:.3f}s, Résultats: {len(results)}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        stats = {
            "num_searches": len(criteria_list),
            "total_time": total_time,
            "avg_search_time": statistics.mean(search_times),
            "total_results": len(all_results),
            "search_times": search_times,
            "session_overhead": total_time - sum(search_times)  # Temps de gestion de session
        }
        
        return stats

    async def benchmark_concurrent_searches(self, criteria_list: List[SearchCriteria]) -> Dict[str, Any]:
        """
        Benchmark de recherches concurrentes (non recommandé avec session partagée).
        
        Args:
            criteria_list: Liste des critères de recherche
            
        Returns:
            Statistiques de performance
        """
        self.logger.info(f"Début du benchmark de recherches concurrentes ({len(criteria_list)} recherches)")
        
        async def single_search(criteria: SearchCriteria) -> tuple:
            async with ConfluenceClient(self.config) as client:
                start_time = time.time()
                results = await client.search_content(criteria)
                end_time = time.time()
                return end_time - start_time, len(results)
        
        start_time = time.time()
        
        # Exécuter toutes les recherches en parallèle
        tasks = [single_search(criteria) for criteria in criteria_list]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        search_times = [result[0] for result in results]
        result_counts = [result[1] for result in results]
        
        stats = {
            "num_searches": len(criteria_list),
            "total_time": total_time,
            "avg_search_time": statistics.mean(search_times),
            "max_search_time": max(search_times),
            "total_results": sum(result_counts),
            "search_times": search_times,
            "speedup_factor": sum(search_times) / total_time if total_time > 0 else 0
        }
        
        return stats

    def print_benchmark_results(self, title: str, stats: Dict[str, Any]):
        """Affiche les résultats du benchmark de manière formatée."""
        print(f"\n{'='*60}")
        print(f"RÉSULTATS DU BENCHMARK: {title}")
        print(f"{'='*60}")
        
        if "iterations" in stats:
            # Benchmark de recherche unique
            print(f"Itérations: {stats['iterations']}")
            print(f"Temps moyen: {stats['avg_time']:.3f}s")
            print(f"Temps min: {stats['min_time']:.3f}s")
            print(f"Temps max: {stats['max_time']:.3f}s")
            print(f"Écart-type: {stats['std_dev']:.3f}s")
            print(f"Résultats moyens: {stats['avg_results']:.1f}")
            print(f"Temps total: {stats['total_time']:.3f}s")
            
        elif "num_searches" in stats:
            # Benchmark de recherches multiples
            print(f"Nombre de recherches: {stats['num_searches']}")
            print(f"Temps total: {stats['total_time']:.3f}s")
            print(f"Temps moyen par recherche: {stats['avg_search_time']:.3f}s")
            print(f"Total des résultats: {stats['total_results']}")
            
            if "session_overhead" in stats:
                print(f"Overhead de session: {stats['session_overhead']:.3f}s")
            
            if "speedup_factor" in stats:
                print(f"Facteur d'accélération: {stats['speedup_factor']:.2f}x")

async def main():
    """Fonction principale exécutant tous les benchmarks."""
    logger = logging.getLogger(__name__)
    
    try:
        # Configuration
        config = ConfluenceConfig.from_env()
        benchmark = SearchPerformanceBenchmark(config)
        
        # Critères de test
        single_criteria = SearchCriteria(
            spaces=["EXAMPLE"],
            types=["page"],
            max_results=20
        )
        
        multiple_criteria = [
            SearchCriteria(spaces=["EXAMPLE"], types=["page"], max_results=10),
            SearchCriteria(spaces=["DOCS"], types=["page"], max_results=10),
            SearchCriteria(labels=["documentation"], max_results=10),
            SearchCriteria(types=["blogpost"], max_results=10),
        ]
        
        # Benchmark 1: Recherche unique répétée
        logger.info("Démarrage du benchmark de recherche unique")
        single_stats = await benchmark.benchmark_single_search(single_criteria, iterations=3)
        benchmark.print_benchmark_results("Recherche Unique (Session Réutilisable)", single_stats)
        
        # Benchmark 2: Recherches multiples séquentielles
        logger.info("Démarrage du benchmark de recherches multiples")
        multiple_stats = await benchmark.benchmark_multiple_searches(multiple_criteria)
        benchmark.print_benchmark_results("Recherches Multiples Séquentielles", multiple_stats)
        
        # Benchmark 3: Recherches concurrentes
        logger.info("Démarrage du benchmark de recherches concurrentes")
        concurrent_stats = await benchmark.benchmark_concurrent_searches(multiple_criteria)
        benchmark.print_benchmark_results("Recherches Concurrentes", concurrent_stats)
        
        # Comparaison finale
        print(f"\n{'='*60}")
        print("COMPARAISON DES APPROCHES")
        print(f"{'='*60}")
        print(f"Séquentiel avec session réutilisable: {multiple_stats['total_time']:.3f}s")
        print(f"Concurrent avec sessions séparées: {concurrent_stats['total_time']:.3f}s")
        
        if multiple_stats['total_time'] > 0:
            improvement = (multiple_stats['total_time'] - concurrent_stats['total_time']) / multiple_stats['total_time'] * 100
            print(f"Amélioration avec concurrence: {improvement:.1f}%")
        
    except Exception as e:
        logger.error(f"Erreur lors du benchmark: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
