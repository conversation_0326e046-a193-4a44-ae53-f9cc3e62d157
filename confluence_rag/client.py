#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Client API Confluence et constructeur de requêtes CQL.
"""

import logging
import time
import aiohttp
import asyncio
import functools
import re
from typing import List, Dict, Any, Optional, Union
from atlassian import Confluence

from .config import ConfluenceConfig, SearchCriteria
from .models import ContentItem, AttachmentDetail, UserInfo, SpaceInfo, LabelInfo
from .utils import <PERSON>try<PERSON>and<PERSON>
from .circuit_breaker import CircuitBreaker
from .thread_pool_manager import get_thread_pool_manager, ThreadPoolManager
from .exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError, CircuitOpenError
)


class ConfluenceClient:
    """Client pour l'API Confluence."""

    @staticmethod
    def _sanitize_error_message(error_message: str) -> str:
        """
        Nettoie un message d'erreur en supprimant les informations sensibles.

        Args:
            error_message: Le message d'erreur original

        Returns:
            Le message d'erreur nettoyé sans informations sensibles
        """
        # Patterns pour identifier et masquer les tokens et informations sensibles
        patterns = [
            # API Tokens classiques - format email:token (doit être avant le pattern PAT général)
            (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}:[A-Za-z0-9]{16,}', '***EMAIL:TOKEN***'),
            # Personal Access Tokens (PAT) - généralement des chaînes alphanumériques longues
            (r'[A-Za-z0-9]{20,}', '***TOKEN***'),
            # Headers Authorization
            (r'Authorization:\s*Bearer\s+[A-Za-z0-9._-]+', 'Authorization: Bearer ***TOKEN***'),
            (r'Authorization:\s*Basic\s+[A-Za-z0-9+/=]+', 'Authorization: Basic ***TOKEN***'),
            # Mots de passe dans les URLs
            (r'://[^:]+:[^@]+@', '://***:***@'),
            # Tokens dans les paramètres d'URL
            (r'([?&])(token|password|secret|key)=([^&\s]+)', r'\1\2=***'),
        ]

        sanitized_message = str(error_message)
        for pattern, replacement in patterns:
            sanitized_message = re.sub(pattern, replacement, sanitized_message, flags=re.IGNORECASE)

        return sanitized_message

    def __init__(self, config: ConfluenceConfig):
        """Initialise le client avec la configuration fournie."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialiser le Circuit Breaker
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker_config)
        self.retry_config = config.retry_config

        # Initialiser le client Confluence en fonction du type de token fourni
        if config.pat_token:
            # Utiliser le Personal Access Token (PAT)
            self.logger.info("Utilisation de l'authentification par Personal Access Token (PAT)")
            self.client = Confluence(
                url=config.url,
                token=config.pat_token,
                timeout=config.timeout
            )
        elif config.api_token and config.username:
            # Utiliser l'API token classique (méthode obsolète mais toujours supportée)
            self.logger.info("Utilisation de l'authentification par API token classique")
            self.client = Confluence(
                url=config.url,
                username=config.username,
                password=config.api_token,
                timeout=config.timeout
            )
        else:
            raise AuthenticationError("Configuration d'authentification invalide: fournissez soit un PAT token, soit un username et API token")

        # Initialiser la session HTTP réutilisable pour les appels optimisés
        self._session = None
        self._session_timeout = aiohttp.ClientTimeout(total=config.timeout)

        self.logger.info(f"Configuration de retry: max_retries={self.retry_config.max_retries}, "
                         f"initial_backoff={self.retry_config.initial_backoff}s, "
                         f"max_backoff={self.retry_config.max_backoff}s")

    async def _get_session(self) -> aiohttp.ClientSession:
        """Obtient ou crée une session HTTP réutilisable."""
        if self._session is None or self._session.closed:
            # Configurer l'authentification en fonction du type de token
            if self.config.pat_token:
                headers = {"Authorization": f"Bearer {self.config.pat_token}"}
                auth = None
            elif self.config.api_token and self.config.username:
                headers = {}
                auth = aiohttp.BasicAuth(self.config.username, self.config.api_token)
            else:
                raise AuthenticationError("Configuration d'authentification invalide")

            # Ajouter les headers par défaut
            headers.update({
                "Accept": "application/json",
                "Content-Type": "application/json"
            })

            self._session = aiohttp.ClientSession(
                timeout=self._session_timeout,
                headers=headers,
                auth=auth
            )
        return self._session

    async def _close_session(self):
        """Ferme la session HTTP si elle existe."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    async def __aenter__(self):
        """Support pour l'utilisation avec async with."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Ferme la session lors de la sortie du contexte."""
        await self._close_session()

    async def _make_cql_request(self, cql: str, start: int = 0, limit: int = 100, expand: str = None) -> Dict[str, Any]:
        """
        Effectue une requête CQL optimisée en utilisant la session HTTP réutilisable.

        Args:
            cql: La requête CQL à exécuter
            start: Index de départ pour la pagination
            limit: Nombre maximum de résultats par page
            expand: Champs à étendre dans la réponse

        Returns:
            La réponse JSON de l'API Confluence
        """
        session = await self._get_session()

        # Construire l'URL de l'API CQL
        url = f"{self.config.url}/wiki/rest/api/search"

        # Paramètres de la requête
        params = {
            "cql": cql,
            "start": start,
            "limit": limit
        }

        if expand:
            params["expand"] = expand

        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 401:
                    raise AuthenticationError("Erreur d'authentification à l'API Confluence")
                elif response.status == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    raise RateLimitExceededError(
                        "Limite de taux d'appels API dépassée",
                        retry_after=retry_after
                    )
                else:
                    error_text = await response.text()
                    sanitized_error = self._sanitize_error_message(error_text)
                    raise APIError(
                        f"Erreur lors de l'appel à l'API Confluence: {response.status}",
                        status_code=response.status
                    )
        except aiohttp.ClientError as e:
            sanitized_error = self._sanitize_error_message(str(e))
            self.logger.error(f"Erreur réseau lors de la requête CQL: {sanitized_error}")
            raise APIError(f"Erreur réseau lors de l'appel à l'API: {sanitized_error}")

    async def _estimate_total_results(self, cql: str) -> int:
        """
        Estime le nombre total de résultats pour une requête CQL.

        Args:
            cql: La requête CQL

        Returns:
            Estimation du nombre total de résultats
        """
        try:
            # Faire une requête avec limit=1 pour obtenir le total
            response = await self._make_cql_request(cql=cql, start=0, limit=1)

            if response and 'size' in response:
                return response['size']
            elif response and 'results' in response:
                # Si 'size' n'est pas disponible, utiliser la longueur des résultats comme estimation minimale
                return len(response['results'])
            else:
                return 0
        except Exception as e:
            self.logger.warning(f"Impossible d'estimer le nombre total de résultats: {e}")
            return 0

    async def _fetch_page_parallel(self, cql: str, start: int, limit: int, expand: str) -> List[Dict[str, Any]]:
        """
        Récupère une page de résultats de manière parallèle.

        Args:
            cql: La requête CQL
            start: Index de départ
            limit: Nombre de résultats par page
            expand: Champs à étendre

        Returns:
            Liste des résultats de la page
        """
        try:
            response = await self._make_cql_request(
                cql=cql,
                start=start,
                limit=limit,
                expand=expand
            )

            if response and 'results' in response:
                return response['results']
            else:
                return []
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de la page {start}-{start+limit}: {e}")
            # Re-lever l'exception pour que la gestion d'erreur globale puisse la traiter
            raise

    def _calculate_parallel_pages(self, total_results: int, max_results: int, page_size: int) -> List[tuple]:
        """
        Calcule les pages à récupérer en parallèle.

        Args:
            total_results: Nombre total de résultats estimé
            max_results: Nombre maximum de résultats souhaités
            page_size: Taille de chaque page

        Returns:
            Liste de tuples (start, limit) pour chaque page
        """
        pages = []
        results_needed = min(total_results, max_results)

        start = 0
        while start < results_needed:
            limit = min(page_size, results_needed - start)
            pages.append((start, limit))
            start += limit

        return pages

    async def _search_content_parallel(self, criteria: SearchCriteria) -> List[ContentItem]:
        """
        Recherche des contenus avec pagination parallèle.

        Args:
            criteria: Critères de recherche

        Returns:
            Liste des contenus trouvés
        """
        cql = criteria.to_cql()
        expand = 'body.storage,body.view,version,space,ancestors,children.page,metadata.labels'
        page_size = min(100, criteria.max_results)  # Taille optimale par page

        self.logger.debug(f"Recherche parallèle avec CQL: {cql}")

        # Estimer le nombre total de résultats
        total_results = await self._estimate_total_results(cql)
        self.logger.info(f"Estimation: {total_results} résultats totaux")

        # Vérifier si la pagination parallèle est justifiée
        if (total_results < self.config.parallel_pagination_threshold or
            criteria.max_results <= page_size):
            self.logger.info("Pagination parallèle non justifiée, utilisation de la méthode séquentielle")
            return await self._search_content_sequential(criteria)

        # Calculer les pages à récupérer
        pages = self._calculate_parallel_pages(total_results, criteria.max_results, page_size)
        self.logger.info(f"Récupération de {len(pages)} pages en parallèle")

        # Limiter le nombre de requêtes parallèles
        max_concurrent = min(self.config.max_parallel_requests, len(pages))

        # Créer un semaphore pour limiter la concurrence
        semaphore = asyncio.Semaphore(max_concurrent)

        async def fetch_page_with_semaphore(start: int, limit: int) -> List[Dict[str, Any]]:
            async with semaphore:
                return await self._fetch_page_parallel(cql, start, limit, expand)

        try:
            # Lancer toutes les requêtes en parallèle
            tasks = [
                fetch_page_with_semaphore(start, limit)
                for start, limit in pages
            ]

            # Attendre que toutes les requêtes se terminent
            page_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Traiter les résultats et gérer les erreurs
            all_results = []
            successful_pages = 0

            for i, page_result in enumerate(page_results):
                if isinstance(page_result, Exception):
                    start, limit = pages[i]
                    self.logger.error(f"Erreur lors de la récupération de la page {start}-{start+limit}: {page_result}")
                    # En cas d'erreur sur une page, on peut soit ignorer soit faire un fallback
                    continue
                else:
                    successful_pages += 1
                    all_results.extend(page_result)

            self.logger.info(f"Pagination parallèle terminée: {successful_pages}/{len(pages)} pages récupérées avec succès")

            # Convertir les résultats en ContentItem et trier par ordre original
            content_items = []
            for result in all_results:
                try:
                    content_item = self._parse_content_response(result)
                    content_items.append(content_item)
                except Exception as e:
                    self.logger.warning(f"Erreur lors du parsing d'un résultat: {e}")
                    continue

            # Limiter aux résultats demandés et retourner
            return content_items[:criteria.max_results]

        except Exception as e:
            self.logger.error(f"Erreur lors de la pagination parallèle: {e}")
            # Fallback vers la méthode séquentielle
            self.logger.info("Fallback vers la pagination séquentielle")
            return await self._search_content_sequential(criteria)

    async def _search_content_sequential(self, criteria: SearchCriteria) -> List[ContentItem]:
        """
        Recherche des contenus avec pagination séquentielle (méthode classique).

        Args:
            criteria: Critères de recherche

        Returns:
            Liste des contenus trouvés
        """
        cql = criteria.to_cql()
        self.logger.debug(f"Recherche séquentielle avec CQL: {cql}")

        results = []
        start = 0
        limit = min(criteria.max_results, 100)  # Limite par page
        expand = 'body.storage,body.view,version,space,ancestors,children.page,metadata.labels'

        while True:
            # Utiliser la méthode optimisée avec session HTTP réutilisable
            response = await self._make_cql_request(
                cql=cql,
                start=start,
                limit=limit,
                expand=expand
            )

            if not response or 'results' not in response:
                break

            for result in response['results']:
                content_item = self._parse_content_response(result)
                results.append(content_item)

            # Vérifier s'il y a plus de résultats
            if len(response['results']) < limit or len(results) >= criteria.max_results:
                break

            start += limit

        self.logger.info(f"Recherche séquentielle terminée: {len(results)} résultats trouvés")
        return results[:criteria.max_results]

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_get_content",
        exceptions_to_trip=(APIError, RateLimitExceededError)
    )
    async def get_content(self, content_id: str) -> ContentItem:
        """Récupère un contenu par son ID."""
        try:
            # Utiliser asyncio.to_thread pour rendre l'appel asynchrone
            # car la bibliothèque atlassian-python-api est synchrone
            content = await asyncio.to_thread(
                self.client.get_page_by_id,
                page_id=content_id,
                expand='body.storage,body.view,version,space,ancestors,children.page,metadata.labels'
            )

            if not content:
                raise ContentNotFoundError(f"Contenu non trouvé avec l'ID: {content_id}")

            return self._parse_content_response(content)
        except Exception as e:
            if "401" in str(e):
                raise AuthenticationError("Erreur d'authentification à l'API Confluence")
            elif "404" in str(e):
                raise ContentNotFoundError(f"Contenu non trouvé avec l'ID: {content_id}")
            elif "429" in str(e):
                retry_after = 60  # Valeur par défaut
                raise RateLimitExceededError(
                    "Limite de taux d'appels API dépassée",
                    retry_after=retry_after
                )
            else:
                sanitized_error = self._sanitize_error_message(str(e))
                self.logger.error(f"Erreur lors de la récupération du contenu {content_id}: {sanitized_error}")
                raise APIError(f"Erreur lors de l'appel à l'API Confluence: {sanitized_error}")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_search_content",
        exceptions_to_trip=(APIError, RateLimitExceededError)
    )
    async def search_content(self, criteria: SearchCriteria) -> List[ContentItem]:
        """
        Recherche des contenus selon les critères spécifiés.

        Version optimisée avec pagination parallèle quand c'est possible,
        sinon fallback vers la pagination séquentielle.
        """
        try:
            # Vérifier si la pagination parallèle est activée et justifiée
            if (self.config.enable_parallel_pagination and
                criteria.max_results > self.config.parallel_pagination_threshold):

                self.logger.info("Utilisation de la pagination parallèle")
                return await self._search_content_parallel(criteria)
            else:
                self.logger.info("Utilisation de la pagination séquentielle")
                return await self._search_content_sequential(criteria)

        except (AuthenticationError, RateLimitExceededError, APIError):
            # Re-lever les exceptions déjà traitées
            raise
        except Exception as e:
            sanitized_error = self._sanitize_error_message(str(e))
            self.logger.error(f"Erreur inattendue lors de la recherche de contenu: {sanitized_error}")
            raise APIError(f"Erreur inattendue lors de l'appel à l'API Confluence: {sanitized_error}")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_get_attachments",
        exceptions_to_trip=(APIError, RateLimitExceededError)
    )
    async def get_attachments(self, content_id: str) -> List[AttachmentDetail]:
        """Récupère les pièces jointes d'un contenu."""
        try:
            # Utiliser asyncio.to_thread pour rendre l'appel asynchrone
            attachments = await asyncio.to_thread(
                self.client.get_attachments_from_content,
                content_id
            )

            if not attachments or 'results' not in attachments:
                return []

            attachment_details = []
            for attachment in attachments['results']:
                attachment_detail = self._parse_attachment_response(attachment, content_id)
                attachment_details.append(attachment_detail)

            return attachment_details
        except Exception as e:
            if "401" in str(e):
                raise AuthenticationError("Erreur d'authentification à l'API Confluence")
            elif "404" in str(e):
                # Retourner une liste vide si le contenu n'existe pas
                self.logger.warning(f"Contenu non trouvé lors de la récupération des pièces jointes: {content_id}")
                return []
            elif "429" in str(e):
                retry_after = 60  # Valeur par défaut
                raise RateLimitExceededError(
                    "Limite de taux d'appels API dépassée",
                    retry_after=retry_after
                )
            else:
                sanitized_error = self._sanitize_error_message(str(e))
                self.logger.error(f"Erreur lors de la récupération des pièces jointes pour {content_id}: {sanitized_error}")
                raise APIError(f"Erreur lors de l'appel à l'API Confluence: {sanitized_error}")

    @RetryHandler.async_retry(
        retry_on_exceptions=(APIError, RateLimitExceededError, aiohttp.ClientError, asyncio.TimeoutError),
    )
    @CircuitBreaker.circuit_breaker(
        service_name="confluence_download_attachment",
        exceptions_to_trip=(APIError, RateLimitExceededError, aiohttp.ClientError)
    )
    async def download_attachment(self, attachment: AttachmentDetail) -> bytes:
        """Télécharge une pièce jointe."""
        try:
            # L'API Atlassian-Python ne fournit pas de méthode asynchrone pour le téléchargement
            # Nous utilisons donc aiohttp directement
            async with aiohttp.ClientSession() as session:
                # Configurer l'authentification en fonction du type de token
                if self.config.pat_token:
                    # Utiliser le Personal Access Token (PAT)
                    headers = {"Authorization": f"Bearer {self.config.pat_token}"}
                    # Ajouter un timeout pour éviter les requêtes bloquées indéfiniment
                    timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                    async with session.get(attachment.download_url, headers=headers, timeout=timeout) as response:
                        if response.status == 200:
                            return await response.read()
                        elif response.status == 401:
                            raise AuthenticationError("Erreur d'authentification lors du téléchargement")
                        elif response.status == 404:
                            raise ContentNotFoundError(f"Pièce jointe non trouvée: {attachment.id}")
                        elif response.status == 429:
                            retry_after = int(response.headers.get('Retry-After', 60))
                            raise RateLimitExceededError(
                                "Limite de taux d'appels API dépassée lors du téléchargement",
                                retry_after=retry_after
                            )
                        else:
                            raise APIError(
                                f"Erreur lors du téléchargement de la pièce jointe: {response.status}",
                                status_code=response.status
                            )
                elif self.config.api_token and self.config.username:
                    # Utiliser l'authentification basique avec API token
                    auth = aiohttp.BasicAuth(self.config.username, self.config.api_token)
                    # Ajouter un timeout pour éviter les requêtes bloquées indéfiniment
                    timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                    async with session.get(attachment.download_url, auth=auth, timeout=timeout) as response:
                        if response.status == 200:
                            return await response.read()
                        elif response.status == 401:
                            raise AuthenticationError("Erreur d'authentification lors du téléchargement")
                        elif response.status == 404:
                            raise ContentNotFoundError(f"Pièce jointe non trouvée: {attachment.id}")
                        elif response.status == 429:
                            retry_after = int(response.headers.get('Retry-After', 60))
                            raise RateLimitExceededError(
                                "Limite de taux d'appels API dépassée lors du téléchargement",
                                retry_after=retry_after
                            )
                        else:
                            raise APIError(
                                f"Erreur lors du téléchargement de la pièce jointe: {response.status}",
                                status_code=response.status
                            )
                else:
                    raise AuthenticationError("Configuration d'authentification invalide pour le téléchargement")
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            sanitized_error = self._sanitize_error_message(str(e))
            self.logger.error(f"Erreur réseau lors du téléchargement de {attachment.id}: {sanitized_error}")
            raise APIError(f"Erreur réseau lors du téléchargement: {sanitized_error}")

    def _parse_content_response(self, response: Dict[str, Any]) -> ContentItem:
        """Parse la réponse de l'API pour créer un objet ContentItem."""
        # Extraire les informations de base
        content_id = response.get('id')
        content_type = response.get('type', 'unknown')
        status = response.get('status', 'unknown')
        title = response.get('title', 'Sans titre')

        # Extraire les informations de version
        version = response.get('version', {})

        # Extraire les informations d'espace
        space_data = response.get('space', {})
        space = SpaceInfo(
            id=space_data.get('id', ''),
            key=space_data.get('key', ''),
            name=space_data.get('name', ''),
            type=space_data.get('type', 'global'),
            description=space_data.get('description', {}).get('plain', {}).get('value', None),
            homepage_id=space_data.get('homepage', {}).get('id', None)
        )

        # Extraire les informations de créateur et de dernier modificateur
        creator_data = response.get('history', {}).get('createdBy', {})
        creator = UserInfo(
            id=creator_data.get('accountId', ''),
            username=creator_data.get('username', ''),
            display_name=creator_data.get('displayName', ''),
            email=creator_data.get('email', None),
            picture_url=creator_data.get('profilePicture', {}).get('path', None)
        )

        last_updater_data = response.get('history', {}).get('lastUpdated', {}).get('by', {})
        last_updater = UserInfo(
            id=last_updater_data.get('accountId', ''),
            username=last_updater_data.get('username', ''),
            display_name=last_updater_data.get('displayName', ''),
            email=last_updater_data.get('email', None),
            picture_url=last_updater_data.get('profilePicture', {}).get('path', None)
        )

        # Extraire les dates de création et de dernière mise à jour
        created = response.get('history', {}).get('createdDate', None)
        last_updated = response.get('history', {}).get('lastUpdated', {}).get('when', None)

        # Extraire les URLs
        content_url = f"{self.config.url}/rest/api/content/{content_id}"
        web_ui_url = f"{self.config.url}/pages/viewpage.action?pageId={content_id}"

        # Extraire le contenu du corps
        body_storage = response.get('body', {}).get('storage', {}).get('value', None)
        body_view = response.get('body', {}).get('view', {}).get('value', None)

        # Extraire les labels
        labels_data = response.get('metadata', {}).get('labels', {}).get('results', [])
        labels = []
        for label_data in labels_data:
            label = LabelInfo(
                id=label_data.get('id', ''),
                name=label_data.get('name', ''),
                prefix=label_data.get('prefix', None)
            )
            labels.append(label)

        # Extraire les informations sur les ancêtres et les enfants
        ancestors = response.get('ancestors', [])
        children = response.get('children', {}).get('page', {}).get('results', [])
        parent_id = ancestors[-1]['id'] if ancestors else None

        # Créer l'objet ContentItem
        content_item = ContentItem(
            id=content_id,
            type=content_type,
            status=status,
            title=title,
            space=space,
            version=version,
            created=created,
            creator=creator,
            last_updated=last_updated,
            last_updater=last_updater,
            content_url=content_url,
            web_ui_url=web_ui_url,
            body_storage=body_storage,
            body_view=body_view,
            labels=labels,
            parent_id=parent_id,
            ancestors=ancestors,
            children=children
        )

        return content_item

    def _parse_attachment_response(self, response: Dict[str, Any], content_id: str) -> AttachmentDetail:
        """Parse la réponse de l'API pour créer un objet AttachmentDetail."""
        # Extraire les informations de base
        attachment_id = response.get('id', '')
        title = response.get('title', 'Sans titre')
        file_name = response.get('title', 'unknown.file')
        file_size = response.get('extensions', {}).get('fileSize', 0)
        media_type = response.get('metadata', {}).get('mediaType', 'application/octet-stream')

        # Extraire les informations de créateur
        creator_data = response.get('extensions', {}).get('lastModifier', {})
        creator = UserInfo(
            id=creator_data.get('accountId', ''),
            username=creator_data.get('username', ''),
            display_name=creator_data.get('displayName', ''),
            email=creator_data.get('email', None),
            picture_url=creator_data.get('profilePicture', {}).get('path', None)
        )

        # Extraire la date de création
        created = response.get('extensions', {}).get('lastModified', None)

        # Construire l'URL de téléchargement
        download_url = f"{self.config.url}/download/attachments/{content_id}/{attachment_id}"

        # Créer l'objet AttachmentDetail
        attachment_detail = AttachmentDetail(
            id=attachment_id,
            title=title,
            file_name=file_name,
            file_size=file_size,
            media_type=media_type,
            created=created,
            creator=creator,
            download_url=download_url,
            content_id=content_id
        )

        return attachment_detail

    async def close(self):
        """
        Ferme proprement le client et ses ressources.

        Cette méthode doit être appelée pour nettoyer les ressources,
        notamment la session HTTP réutilisable.
        """
        await self._close_session()
        self.logger.info("Client Confluence fermé proprement")