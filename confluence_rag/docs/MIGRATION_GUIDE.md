# Guide de Migration vers les Pools de Threads Optimisés

Ce guide vous accompagne dans la migration de votre code existant vers les pools de threads optimisés du système RAG Confluence.

## Vue d'ensemble

La migration consiste à remplacer :
- `asyncio.to_thread()` par des pools de threads spécialisés
- `ThreadPoolExecutor` locaux par un gestionnaire centralisé
- `loop.run_in_executor()` par des méthodes optimisées

## Étape 1 : Analyse du Code Existant

### Utilisation du Script d'Analyse

```bash
# Analyser votre code pour identifier les patterns à migrer
python confluence_rag/scripts/migrate_to_optimized_threads.py /path/to/your/code --output migration_report.txt

# Examiner le rapport
cat migration_report.txt
```

### Patterns Identifiés

Le script identifie automatiquement :
- ✅ Appels `asyncio.to_thread()`
- ✅ Créations de `ThreadPoolExecutor` locaux
- ✅ Appels `loop.run_in_executor(None, ...)`
- ✅ Appels `loop.run_in_executor(self.executor, ...)`

## Étape 2 : Configuration des Pools de Threads

### Configuration de Base

```python
from confluence_rag.config import ThreadPoolConfig, ProcessingConfig

# Configuration des pools de threads
thread_pool_config = ThreadPoolConfig(
    io_thread_workers=8,           # Threads pour I/O
    document_processing_workers=4,  # Threads pour documents
    api_thread_workers=3,          # Threads pour API
    thread_name_prefix="MonApp",
    max_queue_size=100
)

# Configuration de traitement
processing_config = ProcessingConfig(
    max_parallel_downloads=8,
    thread_pool_config=thread_pool_config
)
```

### Variables d'Environnement

```bash
# Ajoutez à votre fichier .env
IO_THREAD_WORKERS=8
DOCUMENT_PROCESSING_WORKERS=4
API_THREAD_WORKERS=3
THREAD_NAME_PREFIX=MonApp
THREAD_POOL_MAX_QUEUE_SIZE=100
MAX_PARALLEL_DOWNLOADS=8
```

## Étape 3 : Migration du Code

### 3.1 Remplacement d'asyncio.to_thread

**Avant :**
```python
import asyncio

# ❌ Ancienne méthode
content = await asyncio.to_thread(
    self.client.get_page_by_id,
    page_id=content_id
)
```

**Après :**
```python
from confluence_rag.thread_pool_manager import get_thread_pool_manager

# ✅ Nouvelle méthode
self.thread_pool_manager = get_thread_pool_manager()
content = await self.thread_pool_manager.run_in_api_pool(
    self.client.get_page_by_id,
    page_id=content_id
)
```

### 3.2 Remplacement de ThreadPoolExecutor

**Avant :**
```python
from concurrent.futures import ThreadPoolExecutor

class MyProcessor:
    def __init__(self):
        # ❌ Pool local
        self.executor = ThreadPoolExecutor(max_workers=5)
    
    async def process_data(self, data):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self._process, data)
```

**Après :**
```python
from confluence_rag.thread_pool_manager import get_thread_pool_manager

class MyProcessor:
    def __init__(self):
        # ✅ Gestionnaire centralisé
        self.thread_pool_manager = get_thread_pool_manager()
    
    async def process_data(self, data):
        return await self.thread_pool_manager.run_in_document_pool(self._process, data)
```

### 3.3 Remplacement de run_in_executor

**Avant :**
```python
# ❌ Ancienne méthode
loop = asyncio.get_event_loop()

# Pour I/O
result = await loop.run_in_executor(None, self._read_file, file_path)

# Pour traitement
result = await loop.run_in_executor(self.executor, self._extract_text, data)
```

**Après :**
```python
# ✅ Nouvelle méthode
thread_manager = get_thread_pool_manager()

# Pour I/O
result = await thread_manager.run_in_io_pool(self._read_file, file_path)

# Pour traitement
result = await thread_manager.run_in_document_pool(self._extract_text, data)
```

## Étape 4 : Choix du Type de Pool

### Pool I/O (`io`)
Utilisez pour :
- Lecture/écriture de fichiers
- Opérations sur le système de fichiers
- Accès à la base de données
- Opérations réseau simples

```python
result = await thread_manager.run_in_io_pool(read_file, path)
```

### Pool Documents (`document`)
Utilisez pour :
- Extraction de texte (PDF, DOCX, XLSX)
- Traitement de documents
- Opérations CPU intensives
- Parsing de données

```python
text = await thread_manager.run_in_document_pool(extract_pdf_text, pdf_data)
```

### Pool API (`api`)
Utilisez pour :
- Appels API synchrones
- Bibliothèques tierces synchrones
- Opérations réseau bloquantes

```python
response = await thread_manager.run_in_api_pool(api_call, params)
```

## Étape 5 : Utilisation du Décorateur (Optionnel)

### Automatisation avec @thread_pool_executor

```python
from confluence_rag.thread_pool_manager import thread_pool_executor

@thread_pool_executor(pool_type='document')
def extract_text_from_pdf(pdf_data):
    # Cette fonction sera automatiquement exécutée dans le pool de documents
    return extract_text(pdf_data)

# Utilisation
text = await extract_text_from_pdf(pdf_data)
```

## Étape 6 : Tests et Validation

### Test de Base

```python
import asyncio
from confluence_rag.thread_pool_manager import get_thread_pool_manager

async def test_migration():
    manager = get_thread_pool_manager()
    
    # Test simple
    result = await manager.run_in_io_pool(lambda: "Test réussi!")
    print(result)
    
    # Vérifier les statistiques
    stats = manager.get_pool_stats()
    print("Statistiques:", stats)

asyncio.run(test_migration())
```

### Test de Performance

```python
import time
import asyncio

async def benchmark_pools():
    manager = get_thread_pool_manager()
    
    def cpu_task():
        return sum(i * i for i in range(10000))
    
    start_time = time.time()
    
    # Exécuter plusieurs tâches en parallèle
    tasks = [manager.run_in_document_pool(cpu_task) for _ in range(10)]
    results = await asyncio.gather(*tasks)
    
    execution_time = time.time() - start_time
    print(f"10 tâches exécutées en {execution_time:.3f}s")

asyncio.run(benchmark_pools())
```

## Étape 7 : Configuration pour Différents Environnements

### Développement
```python
ThreadPoolConfig(
    io_thread_workers=4,
    document_processing_workers=2,
    api_thread_workers=2
)
```

### Test
```python
ThreadPoolConfig(
    io_thread_workers=6,
    document_processing_workers=3,
    api_thread_workers=3
)
```

### Production
```python
ThreadPoolConfig(
    io_thread_workers=12,
    document_processing_workers=8,
    api_thread_workers=6
)
```

## Étape 8 : Monitoring et Debugging

### Surveillance des Pools

```python
def monitor_pools():
    manager = get_thread_pool_manager()
    stats = manager.get_pool_stats()
    
    for pool_name, pool_stats in stats.items():
        print(f"Pool {pool_name}:")
        print(f"  Workers max: {pool_stats['max_workers']}")
        print(f"  Threads actifs: {pool_stats['active_threads']}")
        print(f"  Taille queue: {pool_stats['queue_size']}")
```

### Logs Structurés

```python
import logging

# Configurer le logging pour voir les détails des pools
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('confluence_rag.thread_pool_manager')
```

## Étape 9 : Fermeture Propre

### Nettoyage des Ressources

```python
from confluence_rag.thread_pool_manager import shutdown_global_thread_pools

# À la fin de votre application
shutdown_global_thread_pools()
```

### Utilisation avec Context Manager

```python
from confluence_rag.thread_pool_manager import ThreadPoolManager

async def main():
    with ThreadPoolManager() as manager:
        # Utiliser le manager
        result = await manager.run_in_io_pool(some_function)
    # Les pools sont automatiquement fermés
```

## Checklist de Migration

- [ ] ✅ Analyser le code avec le script de migration
- [ ] ✅ Configurer les pools de threads
- [ ] ✅ Remplacer `asyncio.to_thread`
- [ ] ✅ Remplacer `ThreadPoolExecutor` locaux
- [ ] ✅ Remplacer `run_in_executor`
- [ ] ✅ Choisir les bons types de pools
- [ ] ✅ Tester les performances
- [ ] ✅ Configurer le monitoring
- [ ] ✅ Implémenter la fermeture propre
- [ ] ✅ Déployer et surveiller

## Dépannage

### Problèmes Courants

1. **ImportError** : Vérifiez que le module `thread_pool_manager` est importé
2. **Pool fermé** : Assurez-vous de ne pas utiliser les pools après `shutdown()`
3. **Performance dégradée** : Ajustez le nombre de threads selon votre charge

### Support

- Consultez la documentation : `confluence_rag/docs/THREAD_POOL_OPTIMIZATION.md`
- Exécutez l'exemple : `confluence_rag/examples/migration_example.py`
- Utilisez le script d'analyse pour identifier les problèmes

## Conclusion

La migration vers les pools de threads optimisés apporte des améliorations significatives en performance et en gestion des ressources. Suivez ce guide étape par étape pour une migration réussie.
