# Guide de la Pagination Parallèle - Client Confluence

## Vue d'ensemble

La pagination parallèle est une fonctionnalité avancée du client Confluence qui permet de récupérer de gros volumes de données de manière optimisée en exécutant plusieurs requêtes simultanément.

## Fonctionnement

### Algorithme

1. **Estimation** : Requête initiale pour estimer le nombre total de résultats
2. **Planification** : Calcul des pages à récupérer en parallèle
3. **Exécution** : Lancement simultané des requêtes avec contrôle de concurrence
4. **Assemblage** : Fusion des résultats et gestion des erreurs partielles
5. **Fallback** : Retour automatique à la pagination séquentielle si nécessaire

### Déclenchement Automatique

La pagination parallèle s'active automatiquement quand :
- `enable_parallel_pagination = True`
- `max_results > parallel_pagination_threshold`
- Estimation du total > seuil configuré

## Configuration

### Paramètres Principaux

```python
config = ConfluenceConfig.from_env()
config.enable_parallel_pagination = True        # Activer la fonctionnalité
config.max_parallel_requests = 3               # Nombre de requêtes simultanées
config.parallel_pagination_threshold = 200     # Seuil d'activation
```

### Variables d'Environnement

```bash
ENABLE_PARALLEL_PAGINATION=true
MAX_PARALLEL_REQUESTS=3
PARALLEL_PAGINATION_THRESHOLD=200
```

### Recommandations de Configuration

| Cas d'usage | max_parallel_requests | threshold | Justification |
|-------------|----------------------|-----------|---------------|
| **Conservative** | 2 | 300 | Minimise la charge sur l'API |
| **Équilibré** | 3 | 200 | Bon compromis performance/stabilité |
| **Agressif** | 5 | 100 | Performance maximale |
| **Développement** | 2 | 50 | Tests et debugging |

## Utilisation

### Exemple Basique

```python
import asyncio
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

async def example_parallel_search():
    config = ConfluenceConfig.from_env()
    config.enable_parallel_pagination = True
    config.max_parallel_requests = 3
    
    async with ConfluenceClient(config) as client:
        criteria = SearchCriteria(
            spaces=["DOCS", "TECH"],
            max_results=500  # Déclenche la pagination parallèle
        )
        
        results = await client.search_content(criteria)
        print(f"Récupéré {len(results)} résultats")

asyncio.run(example_parallel_search())
```

### Comparaison des Performances

```python
import time

async def compare_methods():
    criteria = SearchCriteria(max_results=300)
    
    # Test séquentiel
    config_seq = ConfluenceConfig.from_env()
    config_seq.enable_parallel_pagination = False
    
    async with ConfluenceClient(config_seq) as client:
        start = time.time()
        results_seq = await client.search_content(criteria)
        time_seq = time.time() - start
    
    # Test parallèle
    config_par = ConfluenceConfig.from_env()
    config_par.enable_parallel_pagination = True
    
    async with ConfluenceClient(config_par) as client:
        start = time.time()
        results_par = await client.search_content(criteria)
        time_par = time.time() - start
    
    speedup = time_seq / time_par
    print(f"Accélération: {speedup:.2f}x")
```

## Gestion d'Erreurs

### Stratégies de Récupération

1. **Échec partiel** : Continue avec les pages réussies
2. **Échec critique** : Fallback vers pagination séquentielle
3. **Rate limiting** : Respect automatique des limites API

### Monitoring

```python
import logging

# Activer les logs détaillés
logging.getLogger('confluence_rag.client').setLevel(logging.DEBUG)

async with ConfluenceClient(config) as client:
    results = await client.search_content(criteria)
    # Logs automatiques des performances et erreurs
```

## Optimisation

### Réglage des Paramètres

```python
# Pour des APIs avec rate limiting strict
config.max_parallel_requests = 2
config.parallel_pagination_threshold = 500

# Pour des APIs performantes
config.max_parallel_requests = 5
config.parallel_pagination_threshold = 100
```

### Métriques de Performance

```python
async def measure_performance():
    start_time = time.time()
    results = await client.search_content(criteria)
    execution_time = time.time() - start_time
    
    throughput = len(results) / execution_time
    print(f"Débit: {throughput:.1f} résultats/seconde")
```

## Limitations

### Techniques

- **Ordre des résultats** : Peut varier par rapport à la pagination séquentielle
- **Mémoire** : Consommation accrue pendant l'exécution parallèle
- **Complexité** : Gestion d'erreurs plus sophistiquée

### API Confluence

- **Rate limiting** : Limites par minute/heure à respecter
- **Charge serveur** : Impact sur les performances du serveur
- **Quotas** : Consommation accélérée des quotas d'API

## Debugging

### Logs Utiles

```python
# Configuration de logging détaillé
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Messages clés à surveiller
# - "Utilisation de la pagination parallèle"
# - "Estimation: X résultats totaux"
# - "Récupération de X pages en parallèle"
# - "Pagination parallèle terminée: X/Y pages récupérées"
```

### Tests de Performance

```bash
# Exécuter les benchmarks
python confluence_rag/benchmarks/parallel_pagination_benchmark.py

# Tests unitaires
python -m pytest confluence_rag/tests/test_parallel_pagination.py -v

# Test rapide
python test_parallel_pagination.py
```

## Cas d'Usage Recommandés

### ✅ Idéal pour

- **Export de données** : Synchronisation complète d'espaces
- **Recherches étendues** : Critères larges avec beaucoup de résultats
- **Applications batch** : Traitement par lots de contenu
- **Dashboards** : Agrégation de données en temps réel

### ⚠️ À éviter pour

- **Petites requêtes** : < 100 résultats (overhead inutile)
- **APIs limitées** : Serveurs avec rate limiting strict
- **Ressources limitées** : Environnements avec peu de mémoire
- **Ordre critique** : Quand l'ordre exact des résultats est important

## Évolution Future

### Améliorations Prévues

- **Pagination adaptative** : Ajustement automatique selon les performances
- **Cache intelligent** : Mise en cache des estimations de taille
- **Métriques avancées** : Monitoring détaillé des performances
- **Configuration dynamique** : Ajustement en temps réel des paramètres

### Compatibilité

La pagination parallèle est entièrement rétrocompatible :
- Interface publique inchangée
- Fallback automatique vers pagination séquentielle
- Configuration optionnelle (désactivable)

## Support

Pour des questions ou problèmes :
1. Vérifier les logs de debugging
2. Tester avec pagination séquentielle
3. Ajuster les paramètres de configuration
4. Consulter la documentation des performances
