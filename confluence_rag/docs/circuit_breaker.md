# Circuit Breaker Pattern

## Introduction

Le Circuit Breaker Pattern est un mécanisme de résilience qui protège le système contre les défaillances en cascade lorsqu'un service externe devient indisponible ou répond lentement. Ce document explique comment le Circuit Breaker est implémenté dans le système RAG Confluence et comment le configurer.

## Principe de fonctionnement

Le Circuit Breaker fonctionne comme un disjoncteur électrique :

1. **État FERMÉ (CLOSED)** : En fonctionnement normal, le circuit est fermé et les appels au service sont autorisés.
2. **État OUVERT (OPEN)** : Après un certain nombre d'échecs consécutifs, le circuit s'ouvre et les appels sont bloqués pendant une période définie.
3. **État SEMI-OUVERT (HALF-OPEN)** : Après la période d'attente, le circuit passe en état semi-ouvert et autorise un nombre limité d'appels pour tester si le service est de nouveau disponible.

![États du Circuit Breaker](circuit_breaker_states.png)

## Configuration

Le Circuit Breaker peut être configuré via les variables d'environnement suivantes :

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `CIRCUIT_BREAKER_ENABLED` | Activer/désactiver le Circuit Breaker | `true` |
| `CIRCUIT_BREAKER_FAILURE_THRESHOLD` | Nombre d'échecs consécutifs avant d'ouvrir le circuit | `5` |
| `CIRCUIT_BREAKER_RESET_TIMEOUT` | Temps en secondes avant de passer en état semi-ouvert | `60.0` |
| `CIRCUIT_BREAKER_RESET_THRESHOLD` | Nombre de succès consécutifs pour fermer le circuit | `2` |

Ces paramètres peuvent être ajustés dans le fichier `.env` :

```
# Configuration du Circuit Breaker
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RESET_TIMEOUT=60.0
CIRCUIT_BREAKER_RESET_THRESHOLD=2
```

## Implémentation

Le Circuit Breaker est implémenté dans le module `confluence_rag.circuit_breaker` et s'intègre avec le client Confluence pour protéger les appels à l'API.

### Classes principales

- **`CircuitState`** : Énumération des états possibles du circuit (CLOSED, OPEN, HALF_OPEN)
- **`CircuitBreaker`** : Classe principale qui implémente la logique du Circuit Breaker
- **`CircuitBreakerConfig`** : Configuration du Circuit Breaker

### Décorateur

Le Circuit Breaker peut être appliqué à n'importe quelle fonction asynchrone à l'aide du décorateur `@CircuitBreaker.circuit_breaker` :

```python
@CircuitBreaker.circuit_breaker(
    service_name="service_name",
    exceptions_to_trip=(Exception1, Exception2)
)
async def my_function():
    # Code qui appelle un service externe
    pass
```

### Intégration avec le client Confluence

Le Circuit Breaker est appliqué aux méthodes principales du client Confluence :

```python
@RetryHandler.async_retry(
    retry_on_exceptions=(APIError, RateLimitExceededError),
)
@CircuitBreaker.circuit_breaker(
    service_name="confluence_get_content",
    exceptions_to_trip=(APIError, RateLimitExceededError)
)
async def get_content(self, content_id: str) -> ContentItem:
    # Code qui appelle l'API Confluence
    pass
```

## Monitoring

L'état des Circuit Breakers peut être surveillé via l'application de monitoring. Un endpoint `/circuit-breakers` est disponible pour récupérer l'état de tous les Circuit Breakers actifs.

### Exemple de réponse

```json
[
  {
    "service_name": "confluence_get_content",
    "state": "CLOSED",
    "failure_count": 0,
    "success_count": 42,
    "last_state_change": "2023-06-15T14:30:00",
    "time_remaining": null
  },
  {
    "service_name": "confluence_download_attachment",
    "state": "OPEN",
    "failure_count": 7,
    "success_count": 0,
    "last_state_change": "2023-06-15T14:45:00",
    "time_remaining": 30.5
  }
]
```

## Gestion des erreurs

Lorsque le circuit est ouvert, les appels au service protégé échoueront avec une exception `CircuitOpenError`. Cette exception contient des informations sur le service concerné et le temps restant avant que le circuit ne passe en état semi-ouvert.

```python
try:
    content = await client.get_content(content_id)
except CircuitOpenError as e:
    logger.warning(f"Circuit ouvert pour {e.service_name}, réessayer dans {e.time_remaining} secondes")
    # Gérer l'erreur (utiliser une cache, une valeur par défaut, etc.)
```

## Bonnes pratiques

1. **Ajustez les seuils** : Adaptez les seuils d'échec et les délais de réinitialisation en fonction des caractéristiques de votre service externe.
2. **Utilisez des noms de service spécifiques** : Donnez des noms spécifiques à chaque service protégé pour un monitoring plus précis.
3. **Combinez avec d'autres patterns** : Utilisez le Circuit Breaker en combinaison avec d'autres patterns comme le Retry, le Timeout et le Bulkhead.
4. **Prévoyez des alternatives** : Implémentez des stratégies de fallback pour gérer les cas où le circuit est ouvert.

## Conclusion

Le Circuit Breaker Pattern est un outil puissant pour améliorer la résilience de votre système face aux défaillances des services externes. En configurant correctement les paramètres et en surveillant l'état des circuits, vous pouvez éviter les défaillances en cascade et améliorer la disponibilité globale de votre application.
