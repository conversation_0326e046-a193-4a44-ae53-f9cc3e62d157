# Guide de Migration - Sécurité des Logs

## Vue d'ensemble

Ce guide explique comment migrer vers la nouvelle version du système RAG Confluence qui inclut la sécurisation automatique des logs. Cette mise à jour protège automatiquement les informations sensibles (tokens, mots de passe) dans tous les logs.

## Changements apportés

### Nouvelles fonctionnalités

1. **Filtre de sécurité automatique** : Tous les logs sont automatiquement nettoyés
2. **Protection des tokens** : PAT tokens, API tokens masqués automatiquement
3. **Sécurisation des URLs** : Mots de passe dans les URLs protégés
4. **Nettoyage JSON** : Clés sensibles dans les structures JSON masquées
5. **Compatibilité totale** : Fonctionne avec le logging standard et structuré

### Nouveaux fichiers

- `docs/securite_logs.md` : Documentation complète sur la sécurité des logs
- `tests/test_log_security.py` : Tests unitaires pour la sécurisation
- `demo_log_security.py` : Script de démonstration

### Fichiers modifiés

- `confluence_rag/client.py` : Ajout de la méthode `_sanitize_error_message()`
- `confluence_rag/logging_utils.py` : Ajout de la classe `SecurityFilter`
- `confluence_rag/main.py` : Intégration du filtre de sécurité
- `README.md` : Documentation mise à jour
- `.env.example` : Nouvelle option `SECURE_LOGGING`

## Migration étape par étape

### Étape 1 : Mise à jour du code

Aucune modification de code n'est requise ! La sécurisation est automatiquement activée.

### Étape 2 : Configuration (optionnel)

Ajoutez cette ligne à votre fichier `.env` si vous voulez contrôler explicitement la sécurisation :

```bash
# Activer/désactiver la sécurisation des logs (par défaut: true)
SECURE_LOGGING=true
```

### Étape 3 : Vérification

Lancez votre application et vérifiez que les logs ne contiennent plus d'informations sensibles :

```bash
# Lancer l'application
python run_sync.py

# Vérifier les logs
tail -f confluence_rag.log | grep -i "token\|password\|secret"
```

Vous devriez voir des messages comme :
```
2023-06-15 14:32:45 - INFO - Connexion avec token ***TOKEN***
2023-06-15 14:32:46 - ERROR - Erreur d'authentification: ***EMAIL:TOKEN***
```

### Étape 4 : Tests (optionnel)

Exécutez les tests de sécurité pour vérifier le bon fonctionnement :

```bash
# Installer pytest si nécessaire
pip install pytest

# Exécuter les tests de sécurité
python -m pytest tests/test_log_security.py -v

# Exécuter la démonstration
python demo_log_security.py
```

## Compatibilité

### Versions supportées

- **Python** : 3.8+
- **Dépendances** : Aucune nouvelle dépendance requise

### Rétrocompatibilité

✅ **Totalement rétrocompatible** : Aucun changement de code requis dans vos applications existantes.

### Configuration existante

Toutes vos configurations existantes continuent de fonctionner. La sécurisation est activée par défaut.

## Comportement par défaut

### Avant la migration

```python
logger.error("Erreur avec token ABC123XYZ456789")
# Résultat: "Erreur avec token ABC123XYZ456789"
```

### Après la migration

```python
logger.error("Erreur avec token ABC123XYZ456789")
# Résultat: "Erreur avec token ***TOKEN***"
```

## Personnalisation

### Désactiver la sécurisation (non recommandé)

Si vous devez temporairement désactiver la sécurisation pour le debugging :

```bash
# Dans votre fichier .env
SECURE_LOGGING=false
```

⚠️ **Attention** : Ne jamais désactiver en production !

### Ajouter des patterns personnalisés

Pour ajouter vos propres patterns de sécurisation, modifiez la classe `SecurityFilter` :

```python
# Dans confluence_rag/logging_utils.py
patterns = [
    # Patterns existants...
    # Votre pattern personnalisé
    (r'mon_pattern_sensible', 'MASQUE_PERSONNALISE'),
]
```

## Vérification de la migration

### Checklist de migration

- [ ] Code mis à jour vers la nouvelle version
- [ ] Configuration `.env` vérifiée (optionnel)
- [ ] Tests de sécurité exécutés avec succès
- [ ] Logs vérifiés - aucune information sensible visible
- [ ] Application fonctionne normalement

### Tests de validation

1. **Test de base** :
   ```bash
   python demo_log_security.py
   ```

2. **Test complet** :
   ```bash
   python -m pytest tests/test_log_security.py
   ```

3. **Test d'intégration** :
   ```bash
   python run_sync.py
   # Vérifier les logs générés
   ```

## Dépannage

### Problèmes courants

1. **Les logs semblent vides** :
   - Vérifiez le niveau de log (`LOG_LEVEL`)
   - Assurez-vous que l'application génère des logs

2. **Informations sensibles encore visibles** :
   - Vérifiez que `SECURE_LOGGING=true`
   - Redémarrez l'application
   - Vérifiez les patterns de sécurisation

3. **Performance dégradée** :
   - Le filtrage a un impact minimal
   - Réduisez le niveau de log si nécessaire

### Support

Pour obtenir de l'aide :

1. Consultez la [documentation complète](securite_logs.md)
2. Exécutez les tests de diagnostic
3. Vérifiez les logs d'erreur de l'application

## Avantages de la migration

### Sécurité renforcée

- ✅ Protection automatique des tokens d'authentification
- ✅ Masquage des mots de passe dans les URLs
- ✅ Sécurisation des données JSON sensibles
- ✅ Conformité aux bonnes pratiques de sécurité

### Facilité d'utilisation

- ✅ Aucun changement de code requis
- ✅ Configuration automatique
- ✅ Compatible avec tous les types de logging
- ✅ Performance optimisée

### Monitoring amélioré

- ✅ Logs sécurisés pour l'analyse
- ✅ Traçabilité préservée
- ✅ Debugging facilité sans risque de sécurité

## Conclusion

La migration vers la sécurisation automatique des logs est transparente et apporte une protection essentielle contre l'exposition d'informations sensibles. Aucune modification de code n'est requise, et le système continue de fonctionner exactement comme avant, mais de manière plus sécurisée.

Pour toute question ou problème, consultez la documentation détaillée ou les tests fournis.
