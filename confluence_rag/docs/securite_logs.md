# Sécurité des Logs - Système RAG Confluence

## Vue d'ensemble

Ce document décrit les mesures de sécurité mises en place pour protéger les informations sensibles dans les logs du système RAG Confluence, notamment les tokens d'authentification et autres données confidentielles.

## Problématiques de sécurité

### Risques identifiés

1. **Exposition des tokens d'authentification** dans les messages d'erreur
2. **Fuite d'informations sensibles** via les logs de debug
3. **Stockage non sécurisé** des logs contenant des données confidentielles
4. **Transmission non chiffrée** des logs vers des systèmes externes

### Types d'informations sensibles

- Personal Access Tokens (PAT) Confluence
- API Tokens classiques
- Headers d'authentification (Authorization)
- Mots de passe dans les URLs
- Clés d'API dans les configurations JSON
- Informations d'identification utilisateur

## Mesures de protection implémentées

### 1. Nettoyage automatique des messages d'erreur

Le système utilise une fonction `_sanitize_error_message()` dans la classe `ConfluenceClient` qui :

- Identifie automatiquement les patterns de tokens
- Remplace les informations sensibles par des masques
- Préserve la structure du message pour le debugging

```python
# Exemple d'utilisation
sanitized_error = self._sanitize_error_message(str(e))
self.logger.error(f"Erreur lors de la récupération: {sanitized_error}")
```

### 2. Filtre de sécurité global

La classe `SecurityFilter` intercepte tous les logs avant leur écriture :

- Nettoie automatiquement les messages de log
- Traite les arguments des messages formatés
- Applique les patterns de sécurité de manière cohérente

### 3. Patterns de détection

Le système reconnaît et masque automatiquement :

```regex
# Personal Access Tokens (PAT)
[A-Za-z0-9]{20,} → ***TOKEN***

# API Tokens classiques
<EMAIL>:token → ***EMAIL:TOKEN***

# Headers Authorization
Authorization: Bearer token → Authorization: Bearer ***TOKEN***

# Mots de passe dans URLs
://user:pass@host → ://***:***@host

# Paramètres sensibles
?token=value → ?token=***

# Clés JSON
"api_token": "value" → "api_token": "***"
```

## Configuration

### Variables d'environnement

```bash
# Activer le logging sécurisé (par défaut: true)
SECURE_LOGGING=true

# Niveau de log (éviter DEBUG en production)
LOG_LEVEL=INFO

# Format structuré pour une meilleure analyse
STRUCTURED_LOGGING=true
```

### Intégration dans le code

```python
from confluence_rag.logging_utils import SecurityFilter, CorrelationIdFilter

# Configurer les filtres
security_filter = SecurityFilter()
correlation_filter = CorrelationIdFilter()

# Appliquer aux handlers
handler.addFilter(security_filter)
handler.addFilter(correlation_filter)
```

## Bonnes pratiques pour les développeurs

### 1. Éviter les logs sensibles

```python
# ❌ MAUVAIS - Peut exposer des tokens
logger.debug(f"Configuration: {config}")

# ✅ BON - Logs spécifiques sans données sensibles
logger.debug(f"URL configurée: {config.url}")
logger.debug(f"Timeout configuré: {config.timeout}s")
```

### 2. Utiliser le nettoyage manuel si nécessaire

```python
# Pour des cas spéciaux
from confluence_rag.client import ConfluenceClient

sanitized_msg = ConfluenceClient._sanitize_error_message(error_message)
logger.error(f"Erreur: {sanitized_msg}")
```

### 3. Niveaux de log appropriés

- **DEBUG** : Uniquement en développement, jamais en production
- **INFO** : Informations générales sans données sensibles
- **WARNING** : Avertissements avec données nettoyées
- **ERROR** : Erreurs avec messages sanitisés

### 4. Gestion des exceptions

```python
try:
    # Code pouvant lever une exception
    result = api_call()
except Exception as e:
    # Nettoyer automatiquement l'erreur
    sanitized_error = self._sanitize_error_message(str(e))
    logger.error(f"Échec de l'appel API: {sanitized_error}")
    raise APIError(f"Erreur API: {sanitized_error}")
```

## Monitoring et audit

### 1. Vérification des logs

Vérifiez régulièrement que les logs ne contiennent pas :
- Tokens en clair
- Mots de passe
- Clés d'API
- Informations d'identification

### 2. Tests de sécurité

```python
def test_log_security():
    """Test que les informations sensibles sont masquées."""
    sensitive_message = "Error with token ABC123XYZ456"
    sanitized = SecurityFilter._sanitize_message(sensitive_message)
    assert "ABC123XYZ456" not in sanitized
    assert "***TOKEN***" in sanitized
```

### 3. Rotation des logs

- Rotation automatique des fichiers de log
- Chiffrement des logs archivés
- Suppression sécurisée des anciens logs

## Conformité et réglementation

### RGPD

- Aucune donnée personnelle dans les logs
- Pseudonymisation des identifiants utilisateur
- Durée de rétention limitée

### Sécurité d'entreprise

- Logs centralisés avec accès contrôlé
- Audit trail des accès aux logs
- Chiffrement en transit et au repos

## Dépannage

### Problèmes courants

1. **Logs trop verbeux** : Réduire le niveau de log
2. **Informations manquantes** : Vérifier que le nettoyage n'est pas trop agressif
3. **Performance** : Le filtrage peut légèrement impacter les performances

### Désactivation temporaire

```python
# En cas de debugging critique (JAMAIS en production)
import os
os.environ['SECURE_LOGGING'] = 'false'
```

## Mise à jour et maintenance

- Révision régulière des patterns de détection
- Mise à jour selon les nouveaux types de tokens
- Tests de régression sur la sécurité des logs
- Formation des équipes sur les bonnes pratiques
