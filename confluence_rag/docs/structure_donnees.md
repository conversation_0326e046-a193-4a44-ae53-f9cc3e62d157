# Structure des données du système RAG Confluence

Ce document décrit en détail la structure des données utilisée par le système RAG Confluence, notamment l'organisation des pages et des pièces jointes.

## Table des matières

- [Modèles de données](#modèles-de-données)
  - [ContentItem (Page Confluence)](#contentitem-page-confluence)
  - [AttachmentDetail (Pièce jointe)](#attachmentdetail-pièce-jointe)
- [Organisation du stockage](#organisation-du-stockage)
  - [Stockage sur système de fichiers](#stockage-sur-système-de-fichiers)
  - [Stockage sur Google Cloud Storage](#stockage-sur-google-cloud-storage)
- [Format des fichiers JSON](#format-des-fichiers-json)
- [Accès aux données](#accès-aux-données)
- [Relation entre pages et pièces jointes](#relation-entre-pages-et-pièces-jointes)
- [Gestion des pages hiérarchiques](#gestion-des-pages-hiérarchiques)
- [Stratégie de mise à jour](#stratégie-de-mise-à-jour)
- [Exemple d'utilisation](#exemple-dutilisation)
- [Considérations de performance](#considérations-de-performance)

## Modèles de données

### ContentItem (Page Confluence)

Chaque page Confluence est représentée par un objet `ContentItem` avec les attributs suivants :

| Attribut | Type | Description |
|----------|------|-------------|
| `id` | string | Identifiant unique de la page |
| `type` | string | Type de contenu (page, blogpost, etc.) |
| `title` | string | Titre de la page |
| `space` | SpaceInfo | Informations sur l'espace Confluence |
| `body_storage` | string | Contenu HTML brut |
| `body_plain` | string | Contenu en texte brut |
| `attachments` | List[AttachmentDetail] | Liste des pièces jointes |
| `parent_id` | string | ID de la page parente (si applicable) |
| `ancestors` | List[Dict] | Liste des ancêtres de la page |
| `children` | List[Dict] | Liste des pages enfants |

### AttachmentDetail (Pièce jointe)

Chaque pièce jointe est représentée par un objet `AttachmentDetail` avec les attributs suivants :

| Attribut | Type | Description |
|----------|------|-------------|
| `id` | string | Identifiant unique de la pièce jointe |
| `title` | string | Titre de la pièce jointe |
| `file_name` | string | Nom du fichier |
| `file_size` | int | Taille du fichier en octets |
| `media_type` | string | Type MIME du fichier |
| `content_id` | string | ID de la page à laquelle la pièce jointe est associée |
| `download_url` | string | URL de téléchargement |
| `extracted_text` | string | Texte extrait de la pièce jointe (si applicable) |

## Organisation du stockage

Le système organise les données de manière hiérarchique pour faciliter l'accès et la gestion.

### Stockage sur système de fichiers

Lorsque `STORAGE_TYPE=filesystem`, les données sont organisées comme suit :

```
output_data_dir/
├── contents/
│   ├── {content_id_1}.json  # Page avec ID content_id_1
│   ├── {content_id_2}.json  # Page avec ID content_id_2
│   └── ...
├── attachments/
│   ├── {content_id_1}/      # Répertoire pour les pièces jointes de la page content_id_1
│   │   ├── {attachment_id_1}_{filename_1}
│   │   ├── {attachment_id_2}_{filename_2}
│   │   └── ...
│   ├── {content_id_2}/      # Répertoire pour les pièces jointes de la page content_id_2
│   │   ├── {attachment_id_3}_{filename_3}
│   │   └── ...
│   └── ...
└── tracking/                # Données de suivi des changements
    ├── content_hashes/      # Hashes des pages pour détecter les changements
    │   ├── {content_id_1}.json
    │   └── ...
    ├── attachment_hashes/   # Hashes des pièces jointes pour détecter les changements
    │   ├── {attachment_id_1}.json
    │   └── ...
    └── sync_state.json      # État de la dernière synchronisation
```

### Stockage sur Google Cloud Storage

Lorsque `STORAGE_TYPE=gcs`, les données sont organisées comme suit :

```
gs://{bucket_name}/{base_prefix}/
├── contents/
│   ├── {content_id_1}.json
│   ├── {content_id_2}.json
│   └── ...
└── attachments/
    ├── {content_id_1}/
    │   ├── {attachment_id_1}_{filename_1}
    │   ├── {attachment_id_2}_{filename_2}
    │   └── ...
    ├── {content_id_2}/
    │   ├── {attachment_id_3}_{filename_3}
    │   └── ...
    └── ...
```

Le système utilise également un bucket GCS séparé pour stocker les données de suivi si configuré avec `TRACKING_STORAGE_TYPE=gcs`.

## Format des fichiers JSON

### Fichiers de contenu (pages)

Les fichiers JSON stockés dans le répertoire `contents/` contiennent les données des pages Confluence. Voici un exemple de structure :

```json
{
  "id": "123456",
  "type": "page",
  "status": "current",
  "title": "Exemple de page",
  "space": {
    "id": "SPACE123",
    "key": "DOCS",
    "name": "Documentation",
    "type": "global"
  },
  "version": {
    "number": 5,
    "when": "2023-05-15T14:30:00Z",
    "message": "Mise à jour de la documentation"
  },
  "created": "2023-01-10T09:15:00Z",
  "creator": {
    "id": "user123",
    "username": "jdupont",
    "display_name": "Jean Dupont",
    "email": "<EMAIL>"
  },
  "last_updated": "2023-05-15T14:30:00Z",
  "last_updater": {
    "id": "user123",
    "username": "jdupont",
    "display_name": "Jean Dupont",
    "email": "<EMAIL>"
  },
  "content_url": "https://confluence.example.com/rest/api/content/123456",
  "web_ui_url": "https://confluence.example.com/pages/viewpage.action?pageId=123456",
  "body_plain": "Ceci est un exemple de contenu de page Confluence...",
  "labels": [
    {
      "id": "label123",
      "name": "documentation",
      "prefix": "global"
    }
  ],
  "parent_id": "789012",
  "ancestors": [
    {
      "id": "789012",
      "title": "Page parente"
    }
  ],
  "children": [
    {
      "id": "345678",
      "title": "Page enfant"
    }
  ],
  "storage_timestamp": "2023-05-16T10:20:30Z",
  "storage_type": "filesystem"
}
```

### Fichiers de hachage pour le suivi des changements

Les fichiers JSON stockés dans les répertoires `tracking/content_hashes/` et `tracking/attachment_hashes/` contiennent les hachages utilisés pour détecter les changements. Exemple :

```json
{
  "id": "123456",
  "hash": "a1b2c3d4e5f6...",
  "timestamp": "2023-05-16T10:20:30Z"
}
```

### Fichier d'état de synchronisation

Le fichier `tracking/sync_state.json` contient des informations sur la dernière synchronisation :

```json
{
  "last_sync_time": "2023-05-16T10:20:30Z",
  "total_content_items": 150,
  "total_attachments": 320,
  "changed_content_items": 15,
  "changed_attachments": 25,
  "sync_duration_seconds": 45.2
}
```

## Accès aux données

### Récupération des pages

Pour récupérer une page spécifique :

```python
content = await storage_provider.get_content(content_id)
```

### Récupération des pièces jointes

Pour récupérer toutes les pièces jointes d'une page :

```python
attachment_ids = await storage_provider.list_attachments(content_id)
for attachment_id in attachment_ids:
    attachment_data = await storage_provider.get_attachment(content_id, attachment_id)
    # Traiter la pièce jointe...
```

Pour récupérer une pièce jointe spécifique :

```python
attachment_data = await storage_provider.get_attachment(content_id, attachment_id)
```

## Relation entre pages et pièces jointes

Les pièces jointes sont toujours associées à une page via le champ `content_id` dans l'objet `AttachmentDetail`. Cette relation est également reflétée dans la structure de stockage, où les pièces jointes sont organisées dans des répertoires nommés selon l'ID de la page à laquelle elles appartiennent.

Cette organisation hiérarchique permet de :
- Facilement identifier toutes les pièces jointes associées à une page spécifique
- Maintenir une structure claire et organisée dans le système de stockage
- Simplifier la récupération et la gestion des pièces jointes par page

## Gestion des pages hiérarchiques

Confluence organise les pages de manière hiérarchique, avec des relations parent-enfant. Cette hiérarchie est préservée dans le système RAG Confluence de la manière suivante :

### Représentation dans les modèles de données

Chaque objet `ContentItem` contient les informations suivantes sur sa position dans la hiérarchie :

- `parent_id` : ID de la page parente directe
- `ancestors` : Liste des pages ancêtres, du plus proche au plus éloigné
- `children` : Liste des pages enfants directes

### Navigation dans la hiérarchie

Pour naviguer dans la hiérarchie des pages :

1. **Remonter dans la hiérarchie** :
   ```python
   # Récupérer la page parente
   if content.get('parent_id'):
       parent = await storage_provider.get_content(content['parent_id'])
   ```

2. **Descendre dans la hiérarchie** :
   ```python
   # Récupérer les pages enfants
   for child in content.get('children', []):
       child_content = await storage_provider.get_content(child['id'])
   ```

### Synchronisation récursive

Lors de la synchronisation, le système récupère récursivement les pages enfants jusqu'à une profondeur spécifiée (`max_children_depth` dans les critères de recherche). Cela permet de synchroniser des sections entières de l'espace Confluence en une seule opération.

Le processus de récupération récursive fonctionne comme suit :

1. Le système récupère d'abord les pages qui correspondent aux critères de recherche spécifiés.
2. Pour chaque page récupérée, si `include_children` est activé, le système :
   - Examine la liste des pages enfants directes
   - Récupère chaque page enfant avec ses détails complets
   - Si la page enfant a elle-même des enfants et que la profondeur maximale n'est pas atteinte, le système continue récursivement
   - Ce processus continue jusqu'à ce que la profondeur maximale (`max_children_depth`) soit atteinte

Cette fonctionnalité est contrôlée par deux paramètres dans les critères de recherche :
- `include_children` (booléen) : Active ou désactive la récupération récursive des pages enfants
- `max_children_depth` (entier) : Définit la profondeur maximale de récupération (1 = enfants directs uniquement, 2 = enfants et petits-enfants, etc.)

Exemple de configuration dans `criteres_recherche.json` :
```json
{
  "spaces": ["DOCS"],
  "include_children": true,
  "max_children_depth": 3
}
```

Cette configuration récupérera toutes les pages de l'espace "DOCS" qui correspondent aux critères, ainsi que leurs enfants jusqu'à 3 niveaux de profondeur.

## Stratégie de mise à jour

Le système utilise une stratégie de mise à jour intelligente pour optimiser les performances et réduire la charge sur l'API Confluence.

### Téléchargements parallèles

Le système utilise des téléchargements parallèles pour optimiser les performances lors de la récupération des pièces jointes :

1. **Limitation des téléchargements simultanés** :
   - Un sémaphore asyncio est utilisé pour limiter le nombre de téléchargements simultanés
   - Le nombre maximum de téléchargements simultanés est configurable via `MAX_PARALLEL_DOWNLOADS`
   - Cette limitation évite de surcharger l'API Confluence et le réseau

2. **Traitement parallèle des fichiers** :
   - Un `ThreadPoolExecutor` est utilisé pour traiter les fichiers en parallèle
   - Le nombre maximum de workers est configurable via `MAX_THREAD_WORKERS`
   - Cette approche permet de traiter efficacement les fichiers volumineux comme les PDF et les DOCX

3. **Configuration** :
   ```python
   # Dans le fichier .env
   MAX_PARALLEL_DOWNLOADS=5  # Nombre maximum de téléchargements simultanés
   MAX_THREAD_WORKERS=5      # Nombre maximum de workers pour le traitement des fichiers
   ```

### Détection des changements

La détection des changements est basée sur un système de hachage :

1. **Pour les pages** :
   - Un hash est généré à partir des attributs pertinents de la page (ID, titre, version, date de dernière modification) et du contenu
   - Ce hash est stocké dans un fichier JSON dans le répertoire `tracking/content_hashes/`
   - Lors de la prochaine synchronisation, un nouveau hash est généré et comparé avec le hash stocké
   - Si les hashes sont différents, la page est considérée comme modifiée

2. **Pour les pièces jointes** :
   - Un hash est généré à partir des attributs de la pièce jointe (ID, titre, nom de fichier, taille, type, date de création)
   - Ce hash est stocké dans un fichier JSON dans le répertoire `tracking/attachment_hashes/`
   - Lors de la prochaine synchronisation, un nouveau hash est généré et comparé avec le hash stocké
   - Si les hashes sont différents, la pièce jointe est considérée comme modifiée

### Traitement sélectif

Le système ne traite et ne stocke que les contenus qui ont changé depuis la dernière synchronisation :

1. Pour chaque page récupérée depuis Confluence, le système vérifie si elle a changé
2. Si la page a changé, elle est traitée et stockée
3. Pour chaque pièce jointe de la page, le système vérifie si elle a changé
4. Si la pièce jointe a changé, elle est téléchargée, traitée et stockée

### Filtrage des pièces jointes

Les pièces jointes sont filtrées selon plusieurs critères :

1. **Extension de fichier** : Seules les pièces jointes dont l'extension est dans la liste `attachment_extensions_to_convert` sont traitées
2. **Taille maximale** : Les pièces jointes dont la taille dépasse `max_attachment_size_mb` sont ignorées

### Rapport de synchronisation

À la fin de chaque synchronisation, un rapport est généré avec des statistiques sur les contenus traités :

- Nombre total de pages et de pièces jointes
- Nombre de pages et de pièces jointes modifiées
- Nombre de pages et de pièces jointes stockées
- Durée de la synchronisation
- Erreurs éventuelles

## Exemple d'utilisation

### Récupération d'une page et de ses pièces jointes

Voici un exemple de code pour récupérer une page et toutes ses pièces jointes :

```python
# Récupérer la page
content = await storage_provider.get_content(content_id)
if content:
    print(f"Page trouvée: {content['title']}")

    # Récupérer toutes les pièces jointes de la page
    attachment_ids = await storage_provider.list_attachments(content_id)
    print(f"Nombre de pièces jointes: {len(attachment_ids)}")

    for attachment_id in attachment_ids:
        attachment_data = await storage_provider.get_attachment(content_id, attachment_id)
        if attachment_data:
            # Traiter la pièce jointe...
            print(f"Pièce jointe récupérée: {attachment_id}")
```

### Navigation dans la hiérarchie des pages

Voici un exemple de code pour naviguer dans la hiérarchie des pages et récupérer récursivement toutes les pages enfants :

```python
async def retrieve_page_hierarchy(storage_provider, content_id, max_depth=3, current_depth=0):
    """Récupère récursivement une page et toutes ses pages enfants jusqu'à la profondeur spécifiée."""
    if current_depth > max_depth:
        return []

    # Récupérer la page
    content = await storage_provider.get_content(content_id)
    if not content:
        return []

    print(f"{'  ' * current_depth}Page: {content['title']} (ID: {content['id']})")

    # Récupérer les pages enfants
    child_pages = []
    for child_info in content.get('children', []):
        child_id = child_info.get('id')
        if child_id:
            # Récupérer récursivement la page enfant et ses enfants
            child_pages.extend(await retrieve_page_hierarchy(
                storage_provider,
                child_id,
                max_depth,
                current_depth + 1
            ))

    # Ajouter cette page à la liste
    return [content] + child_pages

# Utilisation
root_page_id = "123456"
all_pages = await retrieve_page_hierarchy(storage_provider, root_page_id, max_depth=3)
print(f"Nombre total de pages récupérées: {len(all_pages)}")
```

Cet exemple montre comment récupérer une page et toutes ses pages enfants jusqu'à une profondeur spécifiée, en utilisant une approche récursive similaire à celle utilisée par le système lors de la synchronisation.

## Considérations de performance

### Optimisation de l'accès aux données

Pour optimiser les performances lors de l'accès aux données :

1. **Mise en cache** :
   - Envisagez de mettre en cache les pages fréquemment accédées en mémoire
   - Pour les applications à forte charge, utilisez un système de cache distribué comme Redis

2. **Accès parallèle** :
   - Utilisez `asyncio.gather()` pour récupérer plusieurs pages ou pièces jointes en parallèle
   - Exemple :
     ```python
     content_ids = ["123", "456", "789"]
     contents = await asyncio.gather(*[storage_provider.get_content(id) for id in content_ids])
     ```

3. **Pagination** :
   - Lors de la récupération d'un grand nombre de pages, utilisez la pagination
   - Traitez les pages par lots pour éviter de surcharger la mémoire

4. **Préchargement sélectif** :
   - Préchargez uniquement les données nécessaires
   - Pour les pièces jointes volumineuses, envisagez de les charger à la demande

### Optimisation du stockage

1. **Compression** :
   - Envisagez de compresser les fichiers JSON pour économiser de l'espace
   - Pour GCS, activez la compression au niveau du bucket

2. **Nettoyage périodique** :
   - Implémentez une stratégie de rétention pour supprimer les anciennes versions des pages
   - Supprimez les hashes des pages et pièces jointes qui n'existent plus
