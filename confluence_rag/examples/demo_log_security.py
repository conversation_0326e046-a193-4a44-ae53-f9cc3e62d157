#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de démonstration de la sécurisation des logs.
"""

import logging
import sys
from confluence_rag.logging_utils import SecurityFilter, CorrelationIdFilter, StructuredLogFormatter
from confluence_rag.client import ConfluenceClient


def setup_demo_logging():
    """Configure le logging pour la démonstration."""
    # Créer les filtres
    security_filter = SecurityFilter()
    correlation_filter = CorrelationIdFilter()

    # Créer un formateur simple pour la démonstration
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(correlation_id)s] - %(message)s'
    )

    # Configurer le handler de console
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(security_filter)
    console_handler.addFilter(correlation_filter)

    # Configurer le logger
    logger = logging.getLogger("demo_security")
    logger.setLevel(logging.DEBUG)
    logger.addHandler(console_handler)

    return logger


def demo_log_sanitization():
    """Démontre la sécurisation automatique des logs."""
    logger = setup_demo_logging()

    print("=== DÉMONSTRATION DE LA SÉCURISATION DES LOGS ===\n")

    print("1. Messages avec tokens PAT (Personal Access Tokens):")
    logger.info("Connexion avec token: ABC123XYZ456789012345")
    logger.error("Échec d'authentification avec token ABCDEFGHIJKLMNOPQRSTUVWXYZ123456")

    print("\n2. Messages avec API tokens classiques:")
    logger.info("Authentification: <EMAIL>:SECRETTOKEN123456789")
    logger.warning("Token expiré pour <EMAIL>:OLDTOKEN987654321")

    print("\n3. Messages avec headers Authorization:")
    logger.debug("Requête HTTP: Authorization: Bearer ABC123XYZ456789")
    logger.debug("Authentification Basic: Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==")

    print("\n4. Messages avec URLs contenant des mots de passe:")
    logger.info("Connexion à https://user:<EMAIL>/v1")
    logger.error("Échec de connexion à ftp://admin:<EMAIL>")

    print("\n5. Messages avec paramètres d'URL sensibles:")
    logger.info("Requête GET /api/data?token=SECRET123&user=john&key=APIKEY456")
    logger.warning("URL invalide: /auth?password=mypassword&secret=hidden")

    print("\n6. Messages avec données JSON sensibles:")
    logger.debug('Configuration: {"api_token": "SECRET789", "user": "john", "pat_token": "TOKEN123"}')
    logger.info('Paramètres: {"password": "mypass", "public_data": "visible"}')

    print("\n7. Test de la méthode de nettoyage du ConfluenceClient:")
    error_message = "Authentication failed with token ABC123XYZ456789012345 for <EMAIL>:APITOKEN123"
    sanitized = ConfluenceClient._sanitize_error_message(error_message)
    print(f"Message original: {error_message}")
    print(f"Message nettoyé:  {sanitized}")

    print("\n8. Messages normaux (non sensibles) - doivent rester inchangés:")
    logger.info("Traitement de 100 documents terminé avec succès")
    logger.info("Utilisateur john s'est connecté depuis 192.168.1.100")
    logger.warning("Limite de taux atteinte, retry dans 60 secondes")

    print("\n=== FIN DE LA DÉMONSTRATION ===")


def demo_structured_logging():
    """Démontre le logging structuré avec sécurisation."""
    print("\n=== DÉMONSTRATION DU LOGGING STRUCTURÉ SÉCURISÉ ===\n")

    # Créer les filtres
    security_filter = SecurityFilter()
    correlation_filter = CorrelationIdFilter()

    # Créer un formateur JSON structuré
    formatter = StructuredLogFormatter(include_traceback=True)

    # Configurer le handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(security_filter)
    console_handler.addFilter(correlation_filter)

    # Configurer le logger
    logger = logging.getLogger("demo_structured")
    logger.setLevel(logging.INFO)
    logger.addHandler(console_handler)

    print("Exemple de log structuré avec token sensible:")
    logger.info("Authentification réussie", extra={
        "user_id": "user123",
        "token": "ABC123XYZ456789012345",  # Sera automatiquement masqué
        "endpoint": "/api/v1/auth",
        "response_time": 0.245
    })

    print("\nExemple de log d'erreur structuré:")
    try:
        # Simuler une erreur avec des informations sensibles
        raise ValueError("Connection failed with token SECRET123 for <EMAIL>:APIKEY456")
    except Exception as e:
        logger.error("Erreur de connexion", exc_info=True, extra={
            "error_code": "AUTH_FAILED",
            "retry_count": 3
        })

    print("\n=== FIN DE LA DÉMONSTRATION STRUCTURÉE ===")


if __name__ == "__main__":
    # Démonstration du logging standard avec sécurisation
    demo_log_sanitization()

    # Démonstration du logging structuré avec sécurisation
    demo_structured_logging()

    print("\n" + "="*60)
    print("RÉSUMÉ:")
    print("- Tous les tokens et informations sensibles ont été automatiquement masqués")
    print("- Les messages non sensibles restent inchangés")
    print("- La sécurisation fonctionne avec le logging standard et structuré")
    print("- Les filtres peuvent être appliqués à tous les handlers de logging")
    print("="*60)
