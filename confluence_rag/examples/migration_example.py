#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exemple pratique de migration vers les pools de threads optimisés.
"""

import asyncio
import logging
import time
import os
from typing import List

# Imports pour la version optimisée
from confluence_rag.config import ConfluenceConfig, SearchCriteria, ProcessingConfig, ThreadPoolConfig
from confluence_rag.client import ConfluenceClient
from confluence_rag.processing import ContentRetriever
from confluence_rag.thread_pool_manager import get_thread_pool_manager, shutdown_global_thread_pools


def setup_logging():
    """Configure le logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


async def example_before_optimization():
    """
    Exemple de code AVANT l'optimisation (pour référence).
    
    ⚠️ Ce code utilise les anciennes méthodes et n'est plus recommandé.
    """
    logger = logging.getLogger("before_optimization")
    logger.info("=== Code AVANT optimisation (non recommandé) ===")
    
    # Ancienne méthode : chaque appel asyncio.to_thread crée un nouveau thread
    def simulate_old_approach():
        """Simule l'ancienne approche avec asyncio.to_thread."""
        import threading
        return f"Thread: {threading.current_thread().name}"
    
    start_time = time.time()
    
    # Simulation de plusieurs appels avec l'ancienne méthode
    tasks = []
    for i in range(5):
        # ❌ Ancienne méthode : asyncio.to_thread (crée un nouveau thread à chaque fois)
        task = asyncio.to_thread(simulate_old_approach)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    execution_time = time.time() - start_time
    
    logger.info(f"Ancienne méthode - Temps: {execution_time:.3f}s")
    logger.info(f"Threads utilisés: {len(set(results))}")
    
    return execution_time


async def example_after_optimization():
    """
    Exemple de code APRÈS l'optimisation.
    
    ✅ Ce code utilise les pools de threads optimisés.
    """
    logger = logging.getLogger("after_optimization")
    logger.info("=== Code APRÈS optimisation (recommandé) ===")
    
    # Configuration optimisée des pools de threads
    thread_pool_config = ThreadPoolConfig(
        io_thread_workers=4,
        document_processing_workers=2,
        api_thread_workers=2,
        thread_name_prefix="OptimizedExample"
    )
    
    # Obtenir le gestionnaire de pools optimisé
    thread_manager = get_thread_pool_manager(thread_pool_config)
    
    def simulate_new_approach():
        """Simule la nouvelle approche avec pools optimisés."""
        import threading
        return f"Thread: {threading.current_thread().name}"
    
    start_time = time.time()
    
    # ✅ Nouvelle méthode : pools de threads réutilisables
    tasks = []
    for i in range(5):
        task = thread_manager.run_in_api_pool(simulate_new_approach)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    execution_time = time.time() - start_time
    
    logger.info(f"Nouvelle méthode - Temps: {execution_time:.3f}s")
    logger.info(f"Threads utilisés: {len(set(results))}")
    
    # Afficher les statistiques des pools
    stats = thread_manager.get_pool_stats()
    logger.info("Statistiques des pools:")
    for pool_name, pool_stats in stats.items():
        logger.info(f"  {pool_name}: {pool_stats}")
    
    return execution_time


async def practical_migration_example():
    """
    Exemple pratique de migration d'un cas d'usage réel.
    """
    logger = logging.getLogger("practical_example")
    logger.info("=== Exemple pratique de migration ===")
    
    # Configuration Confluence
    config = ConfluenceConfig.from_env()
    
    # Configuration optimisée des pools de threads
    thread_pool_config = ThreadPoolConfig(
        io_thread_workers=6,
        document_processing_workers=4,
        api_thread_workers=3,
        thread_name_prefix="PracticalExample"
    )
    
    processing_config = ProcessingConfig(
        max_parallel_downloads=6,
        thread_pool_config=thread_pool_config
    )
    
    try:
        async with ConfluenceClient(config) as client:
            # Créer le récupérateur de contenu avec la configuration optimisée
            content_retriever = ContentRetriever(client, processing_config)
            
            # Critères de recherche
            criteria = SearchCriteria(
                spaces=["EXAMPLE"],  # Remplacez par votre espace
                types=["page"],
                max_results=10,
                include_attachments=True
            )
            
            logger.info("Recherche et récupération de contenu avec pools optimisés...")
            start_time = time.time()
            
            # ✅ Cette méthode utilise maintenant les pools de threads optimisés
            content_items = await content_retriever.search_and_retrieve(
                criteria, 
                process_attachments=True
            )
            
            execution_time = time.time() - start_time
            
            logger.info(f"✅ Récupération terminée: {len(content_items)} contenus en {execution_time:.2f}s")
            
            # Afficher les détails des contenus récupérés
            for item in content_items[:3]:  # Afficher les 3 premiers
                logger.info(f"  - {item.title} ({len(item.attachments)} pièces jointes)")
            
            # Afficher les statistiques des pools
            thread_manager = get_thread_pool_manager()
            stats = thread_manager.get_pool_stats()
            logger.info("Statistiques finales des pools:")
            for pool_name, pool_stats in stats.items():
                logger.info(f"  {pool_name}: {pool_stats}")
    
    except Exception as e:
        logger.error(f"Erreur lors de l'exemple pratique: {e}")


async def demonstrate_different_pool_types():
    """
    Démontre l'utilisation des différents types de pools.
    """
    logger = logging.getLogger("pool_types")
    logger.info("=== Démonstration des différents types de pools ===")
    
    thread_manager = get_thread_pool_manager()
    
    # Simulation de différents types de tâches
    def io_intensive_task(task_id):
        """Simule une tâche I/O intensive."""
        import threading
        time.sleep(0.1)  # Simulation d'I/O
        return f"I/O Task {task_id} - Thread: {threading.current_thread().name}"
    
    def document_processing_task(task_id):
        """Simule une tâche de traitement de document."""
        import threading
        # Simulation de traitement CPU intensif
        total = sum(i * i for i in range(1000))
        return f"Doc Task {task_id} - Thread: {threading.current_thread().name} - Result: {total}"
    
    def api_call_task(task_id):
        """Simule un appel API."""
        import threading
        time.sleep(0.05)  # Simulation d'appel réseau
        return f"API Task {task_id} - Thread: {threading.current_thread().name}"
    
    start_time = time.time()
    
    # Exécuter différents types de tâches en parallèle
    tasks = []
    
    # Tâches I/O
    for i in range(3):
        tasks.append(thread_manager.run_in_io_pool(io_intensive_task, i))
    
    # Tâches de traitement de documents
    for i in range(2):
        tasks.append(thread_manager.run_in_document_pool(document_processing_task, i))
    
    # Tâches API
    for i in range(2):
        tasks.append(thread_manager.run_in_api_pool(api_call_task, i))
    
    results = await asyncio.gather(*tasks)
    execution_time = time.time() - start_time
    
    logger.info(f"Toutes les tâches terminées en {execution_time:.3f}s")
    logger.info("Résultats:")
    for result in results:
        logger.info(f"  {result}")
    
    # Afficher les statistiques
    stats = thread_manager.get_pool_stats()
    logger.info("Statistiques des pools après exécution:")
    for pool_name, pool_stats in stats.items():
        logger.info(f"  {pool_name}: {pool_stats}")


async def main():
    """Fonction principale."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Démonstration de la migration vers les pools de threads optimisés")
    
    try:
        # 1. Comparaison avant/après optimisation
        logger.info("\n" + "="*60)
        old_time = await example_before_optimization()
        
        logger.info("\n" + "="*60)
        new_time = await example_after_optimization()
        
        improvement = ((old_time - new_time) / old_time) * 100 if old_time > 0 else 0
        logger.info(f"\n📊 Amélioration des performances: {improvement:.1f}%")
        
        # 2. Exemple pratique avec Confluence
        logger.info("\n" + "="*60)
        await practical_migration_example()
        
        # 3. Démonstration des différents types de pools
        logger.info("\n" + "="*60)
        await demonstrate_different_pool_types()
        
        logger.info("\n🎉 Migration terminée avec succès!")
        
        # Conseils finaux
        logger.info("\n💡 Conseils pour optimiser davantage:")
        logger.info("  1. Ajustez le nombre de threads selon votre charge de travail")
        logger.info("  2. Surveillez les statistiques des pools en production")
        logger.info("  3. Utilisez le décorateur @thread_pool_executor pour automatiser")
        logger.info("  4. Configurez les variables d'environnement pour différents environnements")
    
    except Exception as e:
        logger.error(f"Erreur lors de la démonstration: {e}")
        raise
    
    finally:
        # Fermer proprement les pools de threads
        logger.info("\nFermeture des pools de threads...")
        shutdown_global_thread_pools()
        logger.info("Pools fermés proprement")


if __name__ == "__main__":
    # Charger les variables d'environnement si disponibles
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    # Exécuter l'exemple
    asyncio.run(main())
