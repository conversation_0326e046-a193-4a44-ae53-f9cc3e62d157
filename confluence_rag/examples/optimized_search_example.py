#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exemple d'utilisation du client Confluence optimisé avec session HTTP réutilisable.
"""

import asyncio
import logging
import time
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def example_optimized_search():
    """
    Exemple démontrant l'utilisation optimisée du client Confluence.
    """
    logger = logging.getLogger(__name__)
    
    # Configuration du client
    config = ConfluenceConfig.from_env()
    
    # Utiliser le client avec async context manager pour une gestion automatique des ressources
    async with ConfluenceClient(config) as client:
        logger.info("=== Début de l'exemple de recherche optimisée ===")
        
        # Critères de recherche
        criteria = SearchCriteria(
            spaces=["EXAMPLE", "DOCS"],
            labels=["documentation"],
            types=["page"],
            max_results=50
        )
        
        # Mesurer le temps d'exécution
        start_time = time.time()
        
        try:
            # Effectuer la recherche avec la méthode optimisée
            results = await client.search_content(criteria)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"Recherche terminée en {execution_time:.2f} secondes")
            logger.info(f"Nombre de résultats: {len(results)}")
            
            # Afficher quelques résultats
            for i, content in enumerate(results[:5]):
                logger.info(f"Résultat {i+1}: {content.title} (ID: {content.id})")
                
        except Exception as e:
            logger.error(f"Erreur lors de la recherche: {e}")
            raise

async def example_multiple_searches():
    """
    Exemple démontrant l'avantage de la session réutilisable avec plusieurs recherches.
    """
    logger = logging.getLogger(__name__)
    
    config = ConfluenceConfig.from_env()
    
    # Utiliser le client avec async context manager
    async with ConfluenceClient(config) as client:
        logger.info("=== Début de l'exemple de recherches multiples ===")
        
        # Différents critères de recherche
        search_criteria_list = [
            SearchCriteria(spaces=["EXAMPLE"], types=["page"], max_results=10),
            SearchCriteria(spaces=["DOCS"], types=["blogpost"], max_results=10),
            SearchCriteria(labels=["api"], max_results=10),
        ]
        
        start_time = time.time()
        
        # Effectuer plusieurs recherches avec la même session
        all_results = []
        for i, criteria in enumerate(search_criteria_list):
            logger.info(f"Recherche {i+1}/3...")
            results = await client.search_content(criteria)
            all_results.extend(results)
            logger.info(f"  -> {len(results)} résultats trouvés")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"Toutes les recherches terminées en {execution_time:.2f} secondes")
        logger.info(f"Total des résultats: {len(all_results)}")

async def example_manual_session_management():
    """
    Exemple avec gestion manuelle de la session (non recommandé, mais possible).
    """
    logger = logging.getLogger(__name__)
    
    config = ConfluenceConfig.from_env()
    client = ConfluenceClient(config)
    
    try:
        logger.info("=== Début de l'exemple de gestion manuelle ===")
        
        criteria = SearchCriteria(
            spaces=["EXAMPLE"],
            max_results=5
        )
        
        results = await client.search_content(criteria)
        logger.info(f"Résultats trouvés: {len(results)}")
        
    finally:
        # Important: fermer manuellement le client
        await client.close()
        logger.info("Client fermé manuellement")

async def main():
    """Fonction principale exécutant tous les exemples."""
    logger = logging.getLogger(__name__)
    
    try:
        # Exemple 1: Recherche simple optimisée
        await example_optimized_search()
        
        print("\n" + "="*50 + "\n")
        
        # Exemple 2: Recherches multiples avec session réutilisable
        await example_multiple_searches()
        
        print("\n" + "="*50 + "\n")
        
        # Exemple 3: Gestion manuelle (pour comparaison)
        await example_manual_session_management()
        
    except Exception as e:
        logger.error(f"Erreur dans l'exemple: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
