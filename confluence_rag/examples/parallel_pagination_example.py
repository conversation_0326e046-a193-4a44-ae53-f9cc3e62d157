#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exemple d'utilisation de la pagination parallèle du client Confluence.
"""

import asyncio
import logging
import time
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def example_parallel_pagination():
    """
    Exemple démontrant la pagination parallèle pour de gros volumes de données.
    """
    logger = logging.getLogger(__name__)
    
    # Configuration avec pagination parallèle activée
    config = ConfluenceConfig.from_env()
    config.enable_parallel_pagination = True
    config.max_parallel_requests = 4
    config.parallel_pagination_threshold = 50  # Seuil bas pour la démo
    
    async with ConfluenceClient(config) as client:
        logger.info("=== Exemple de pagination parallèle ===")
        
        # Critères pour déclencher la pagination parallèle
        criteria = SearchCriteria(
            spaces=["EXAMPLE"],  # Remplacez par un espace avec beaucoup de contenu
            types=["page"],
            max_results=300  # Assez grand pour déclencher la pagination parallèle
        )
        
        # Mesurer le temps d'exécution
        start_time = time.time()
        
        try:
            # Effectuer la recherche avec pagination parallèle
            results = await client.search_content(criteria)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"✅ Pagination parallèle terminée en {execution_time:.2f} secondes")
            logger.info(f"📊 Nombre de résultats: {len(results)}")
            
            # Afficher quelques statistiques
            if results:
                logger.info("📄 Premiers résultats:")
                for i, content in enumerate(results[:5]):
                    logger.info(f"  {i+1}. {content.title} (ID: {content.id})")
                    
                logger.info(f"📄 Derniers résultats:")
                for i, content in enumerate(results[-3:], len(results)-2):
                    logger.info(f"  {i}. {content.title} (ID: {content.id})")
                
        except Exception as e:
            logger.error(f"Erreur lors de la pagination parallèle: {e}")
            raise

async def compare_pagination_methods():
    """
    Compare les performances entre pagination séquentielle et parallèle.
    """
    logger = logging.getLogger(__name__)
    
    # Configuration pour les tests
    config = ConfluenceConfig.from_env()
    
    criteria = SearchCriteria(
        spaces=["EXAMPLE"],
        types=["page"],
        max_results=100
    )
    
    logger.info("=== Comparaison des méthodes de pagination ===")
    
    # Test 1: Pagination séquentielle
    config.enable_parallel_pagination = False
    async with ConfluenceClient(config) as client:
        logger.info("🔄 Test de pagination séquentielle...")
        start_time = time.time()
        
        results_sequential = await client.search_content(criteria)
        
        sequential_time = time.time() - start_time
        logger.info(f"⏱️  Séquentielle: {sequential_time:.2f}s, {len(results_sequential)} résultats")
    
    # Test 2: Pagination parallèle
    config.enable_parallel_pagination = True
    config.parallel_pagination_threshold = 50  # Seuil bas pour forcer la pagination parallèle
    config.max_parallel_requests = 3
    
    async with ConfluenceClient(config) as client:
        logger.info("🚀 Test de pagination parallèle...")
        start_time = time.time()
        
        results_parallel = await client.search_content(criteria)
        
        parallel_time = time.time() - start_time
        logger.info(f"⏱️  Parallèle: {parallel_time:.2f}s, {len(results_parallel)} résultats")
    
    # Comparaison
    if sequential_time > 0:
        speedup = sequential_time / parallel_time if parallel_time > 0 else float('inf')
        improvement = ((sequential_time - parallel_time) / sequential_time) * 100
        
        logger.info(f"📈 Amélioration: {improvement:.1f}% (facteur {speedup:.2f}x)")
        
        if len(results_sequential) == len(results_parallel):
            logger.info("✅ Même nombre de résultats obtenus")
        else:
            logger.warning(f"⚠️  Différence dans le nombre de résultats: {len(results_sequential)} vs {len(results_parallel)}")

async def example_configuration_tuning():
    """
    Exemple de réglage des paramètres de pagination parallèle.
    """
    logger = logging.getLogger(__name__)
    
    logger.info("=== Réglage des paramètres de pagination ===")
    
    # Différentes configurations à tester
    configs = [
        {"max_parallel_requests": 2, "threshold": 100},
        {"max_parallel_requests": 3, "threshold": 100},
        {"max_parallel_requests": 5, "threshold": 100},
    ]
    
    criteria = SearchCriteria(
        spaces=["EXAMPLE"],
        max_results=200
    )
    
    for i, config_params in enumerate(configs):
        logger.info(f"🔧 Configuration {i+1}: {config_params}")
        
        config = ConfluenceConfig.from_env()
        config.enable_parallel_pagination = True
        config.max_parallel_requests = config_params["max_parallel_requests"]
        config.parallel_pagination_threshold = config_params["threshold"]
        
        async with ConfluenceClient(config) as client:
            start_time = time.time()
            
            try:
                results = await client.search_content(criteria)
                execution_time = time.time() - start_time
                
                logger.info(f"  ⏱️  Temps: {execution_time:.2f}s, Résultats: {len(results)}")
                
            except Exception as e:
                logger.error(f"  ❌ Erreur: {e}")

async def example_error_handling():
    """
    Exemple de gestion d'erreurs avec pagination parallèle.
    """
    logger = logging.getLogger(__name__)
    
    logger.info("=== Gestion d'erreurs en pagination parallèle ===")
    
    config = ConfluenceConfig.from_env()
    config.enable_parallel_pagination = True
    config.max_parallel_requests = 10  # Volontairement élevé pour tester les limites
    config.parallel_pagination_threshold = 50
    
    # Critères qui pourraient causer des erreurs
    criteria = SearchCriteria(
        spaces=["NONEXISTENT"],  # Espace qui n'existe pas
        max_results=500
    )
    
    async with ConfluenceClient(config) as client:
        try:
            results = await client.search_content(criteria)
            logger.info(f"✅ Recherche réussie malgré l'espace inexistant: {len(results)} résultats")
            
        except Exception as e:
            logger.info(f"🔄 Fallback vers pagination séquentielle après erreur: {e}")

async def main():
    """Fonction principale exécutant tous les exemples."""
    logger = logging.getLogger(__name__)
    
    try:
        # Exemple 1: Pagination parallèle basique
        await example_parallel_pagination()
        
        print("\n" + "="*60 + "\n")
        
        # Exemple 2: Comparaison des méthodes
        await compare_pagination_methods()
        
        print("\n" + "="*60 + "\n")
        
        # Exemple 3: Réglage des paramètres
        await example_configuration_tuning()
        
        print("\n" + "="*60 + "\n")
        
        # Exemple 4: Gestion d'erreurs
        await example_error_handling()
        
        logger.info("🎉 Tous les exemples de pagination parallèle terminés!")
        
    except Exception as e:
        logger.error(f"❌ Erreur dans les exemples: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
