#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Exemple démontrant les optimisations de gestion des threads dans le système RAG Confluence.
"""

import asyncio
import logging
import time
import os
from typing import List

from confluence_rag.config import ConfluenceConfig, SearchCriteria, ProcessingConfig, ThreadPoolConfig
from confluence_rag.client import ConfluenceClient
from confluence_rag.processing import AttachmentProcessor, ContentRetriever
from confluence_rag.thread_pool_manager import get_thread_pool_manager, shutdown_global_thread_pools


def setup_logging():
    """Configure le logging pour l'exemple."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


async def demonstrate_thread_pool_optimization():
    """
    Démontre les optimisations de gestion des threads.
    """
    logger = logging.getLogger(__name__)
    
    logger.info("=== Démonstration des optimisations de pools de threads ===")
    
    # Configuration optimisée des pools de threads
    thread_pool_config = ThreadPoolConfig(
        io_thread_workers=6,           # Plus de threads pour les I/O
        document_processing_workers=4,  # Threads dédiés au traitement de documents
        api_thread_workers=3,          # Threads pour les appels API synchrones
        thread_name_prefix="OptimizedRAG",
        max_queue_size=50
    )
    
    processing_config = ProcessingConfig(
        max_parallel_downloads=8,      # Plus de téléchargements simultanés
        thread_pool_config=thread_pool_config
    )
    
    # Configuration Confluence
    config = ConfluenceConfig.from_env()
    config.enable_parallel_pagination = True
    config.max_parallel_requests = 4
    config.parallel_pagination_threshold = 100
    
    logger.info("Configuration des pools de threads:")
    logger.info(f"  - Threads I/O: {thread_pool_config.io_thread_workers}")
    logger.info(f"  - Threads traitement documents: {thread_pool_config.document_processing_workers}")
    logger.info(f"  - Threads API: {thread_pool_config.api_thread_workers}")
    logger.info(f"  - Téléchargements parallèles: {processing_config.max_parallel_downloads}")
    
    # Obtenir le gestionnaire de pools de threads
    thread_manager = get_thread_pool_manager(thread_pool_config)
    
    # Afficher les statistiques initiales
    logger.info("Statistiques initiales des pools:")
    stats = thread_manager.get_pool_stats()
    for pool_name, pool_stats in stats.items():
        logger.info(f"  {pool_name}: {pool_stats}")
    
    try:
        async with ConfluenceClient(config) as client:
            # Test 1: Recherche de contenu avec pagination parallèle optimisée
            logger.info("\n🚀 Test 1: Recherche de contenu avec pools optimisés")
            
            criteria = SearchCriteria(
                spaces=["EXAMPLE"],  # Remplacez par votre espace
                types=["page"],
                max_results=200,
                include_attachments=True
            )
            
            start_time = time.time()
            content_items = await client.search_content(criteria)
            search_time = time.time() - start_time
            
            logger.info(f"✅ Recherche terminée: {len(content_items)} résultats en {search_time:.2f}s")
            
            # Test 2: Traitement de contenu avec pools optimisés
            if content_items:
                logger.info("\n📄 Test 2: Traitement de contenu avec pools optimisés")
                
                content_retriever = ContentRetriever(client, processing_config)
                
                # Traiter quelques contenus pour démontrer l'optimisation
                sample_items = content_items[:3]  # Prendre les 3 premiers
                
                start_time = time.time()
                detailed_items = []
                
                for item in sample_items:
                    try:
                        detailed_item = await content_retriever.retrieve_content(
                            item.id, 
                            process_attachments=True
                        )
                        detailed_items.append(detailed_item)
                        
                        logger.info(f"  ✅ Contenu traité: {item.title}")
                        
                        # Afficher les statistiques des pools pendant le traitement
                        stats = thread_manager.get_pool_stats()
                        logger.debug(f"  Stats pools: {stats}")
                        
                    except Exception as e:
                        logger.error(f"  ❌ Erreur lors du traitement de {item.id}: {e}")
                
                processing_time = time.time() - start_time
                logger.info(f"✅ Traitement terminé: {len(detailed_items)} contenus en {processing_time:.2f}s")
                
                # Afficher les statistiques finales
                logger.info("\nStatistiques finales des pools:")
                stats = thread_manager.get_pool_stats()
                for pool_name, pool_stats in stats.items():
                    logger.info(f"  {pool_name}: {pool_stats}")
                
                # Test 3: Démonstration des différents types de pools
                logger.info("\n🔧 Test 3: Démonstration des différents types de pools")
                
                # Tâche I/O
                async def io_task():
                    return await thread_manager.run_in_io_pool(
                        lambda: f"Tâche I/O exécutée dans le thread {os.getpid()}"
                    )
                
                # Tâche de traitement de document (simulation)
                async def document_task():
                    return await thread_manager.run_in_document_pool(
                        lambda: f"Tâche de traitement de document dans le thread {os.getpid()}"
                    )
                
                # Tâche API (simulation)
                async def api_task():
                    return await thread_manager.run_in_api_pool(
                        lambda: f"Tâche API dans le thread {os.getpid()}"
                    )
                
                # Exécuter les tâches en parallèle
                start_time = time.time()
                results = await asyncio.gather(
                    io_task(),
                    document_task(),
                    api_task(),
                    return_exceptions=True
                )
                parallel_time = time.time() - start_time
                
                logger.info(f"✅ Tâches parallèles terminées en {parallel_time:.3f}s:")
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"  ❌ Tâche {i+1}: {result}")
                    else:
                        logger.info(f"  ✅ Tâche {i+1}: {result}")
            
            else:
                logger.warning("Aucun contenu trouvé pour les tests")
    
    except Exception as e:
        logger.error(f"Erreur lors de la démonstration: {e}")
        raise
    
    finally:
        # Afficher les statistiques finales avant fermeture
        logger.info("\nStatistiques finales avant fermeture:")
        stats = thread_manager.get_pool_stats()
        for pool_name, pool_stats in stats.items():
            logger.info(f"  {pool_name}: {pool_stats}")


async def benchmark_thread_pools():
    """
    Compare les performances avec et sans optimisation des pools de threads.
    """
    logger = logging.getLogger(__name__)
    
    logger.info("\n=== Benchmark des pools de threads ===")
    
    # Configuration de base
    config = ConfluenceConfig.from_env()
    
    # Test avec configuration standard (ancienne méthode)
    logger.info("🔄 Test avec configuration standard...")
    
    standard_config = ProcessingConfig(
        max_thread_workers=3,  # Ancienne configuration
        max_parallel_downloads=3
    )
    
    # Test avec configuration optimisée
    logger.info("🚀 Test avec configuration optimisée...")
    
    optimized_thread_config = ThreadPoolConfig(
        io_thread_workers=8,
        document_processing_workers=6,
        api_thread_workers=4
    )
    
    optimized_config = ProcessingConfig(
        max_parallel_downloads=8,
        thread_pool_config=optimized_thread_config
    )
    
    # Critères de test
    criteria = SearchCriteria(
        spaces=["EXAMPLE"],
        types=["page"],
        max_results=50,
        include_attachments=True
    )
    
    try:
        # Test optimisé
        async with ConfluenceClient(config) as client:
            content_retriever = ContentRetriever(client, optimized_config)
            
            start_time = time.time()
            results = await content_retriever.search_and_retrieve(criteria, process_attachments=True)
            optimized_time = time.time() - start_time
            
            logger.info(f"✅ Configuration optimisée: {len(results)} résultats en {optimized_time:.2f}s")
            
            # Afficher les statistiques des pools
            thread_manager = get_thread_pool_manager()
            stats = thread_manager.get_pool_stats()
            logger.info("Statistiques des pools optimisés:")
            for pool_name, pool_stats in stats.items():
                logger.info(f"  {pool_name}: {pool_stats}")
    
    except Exception as e:
        logger.error(f"Erreur lors du benchmark: {e}")


async def main():
    """Fonction principale."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Démonstration des optimisations
        await demonstrate_thread_pool_optimization()
        
        # Benchmark des performances
        await benchmark_thread_pools()
        
        logger.info("\n🎉 Démonstration terminée avec succès!")
        
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution: {e}")
        raise
    
    finally:
        # Fermer proprement les pools de threads
        logger.info("Fermeture des pools de threads...")
        shutdown_global_thread_pools()
        logger.info("Pools de threads fermés")


if __name__ == "__main__":
    # Charger les variables d'environnement si disponibles
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    # Exécuter l'exemple
    asyncio.run(main())
