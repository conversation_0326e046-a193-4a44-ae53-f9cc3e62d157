#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Orchestration du processus de synchronisation pour le système RAG Confluence.
"""

import os
import logging
import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

from .config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from .client import ConfluenceClient
from .processing import ContentRetriever
from .tracking import ConfluenceChangeTracker
from .storage import get_storage_provider, StorageProvider
from .models import ContentItem, AttachmentDetail
from .exceptions import (
    ConfluenceRAGException, AuthenticationError, APIError,
    RateLimitExceededError, ContentProcessingError
)
from .logging_utils import CorrelationContext, propagate_correlation_id


class SyncOrchestrator:
    """Orchestrateur du processus de synchronisation."""

    def __init__(
        self,
        config: ConfluenceConfig,
        criteria: SearchCriteria,
        storage_config: StorageConfig = None,
        processing_config: ProcessingConfig = None
    ):
        """
        Initialise l'orchestrateur avec la configuration et les critères de recherche.

        Args:
            config: Configuration Confluence
            criteria: Critères de recherche
            storage_config: Configuration de stockage (optionnelle)
            processing_config: Configuration de traitement (optionnelle)
        """
        self.config = config
        self.criteria = criteria
        self.storage_config = storage_config or StorageConfig()
        self.processing_config = processing_config or ProcessingConfig.from_env()
        self.client = ConfluenceClient(config)
        self.content_retriever = ContentRetriever(self.client, self.processing_config)
        self.change_tracker = ConfluenceChangeTracker()
        self.logger = logging.getLogger(__name__)

        # Initialiser le fournisseur de stockage
        if self.storage_config.storage_type == "filesystem":
            self.storage = get_storage_provider("filesystem", base_dir=self.storage_config.output_dir)
        elif self.storage_config.storage_type == "gcs":
            self.storage = get_storage_provider("gcs",
                                              bucket_name=self.storage_config.gcs_bucket_name,
                                              base_prefix=self.storage_config.gcs_base_prefix)
        else:
            raise ValueError(f"Type de stockage non supporté: {self.storage_config.storage_type}")

        self.logger.info(
            f"SyncOrchestrator initialisé avec max_parallel_downloads={self.processing_config.max_parallel_downloads}, "
            f"max_thread_workers={self.processing_config.max_thread_workers}"
        )

        # Statistiques de synchronisation
        self.stats = {
            "start_time": None,
            "end_time": None,
            "total_content_items": 0,
            "total_attachments": 0,
            "changed_content_items": 0,
            "changed_attachments": 0,
            "stored_content_items": 0,
            "stored_attachments": 0,
            "errors": 0,
            "warnings": 0,
            "storage_type": self.storage_config.storage_type
        }

    async def run(self) -> Dict[str, Any]:
        """Exécute le processus de synchronisation complet."""
        self.stats["start_time"] = datetime.now()
        self.logger.info(f"Début de la synchronisation avec Confluence: {self.config.url}")

        try:
            # Rechercher et récupérer les contenus
            content_items = await self._retrieve_content()

            # Traiter les contenus modifiés
            changed_items = await self._process_changed_content(content_items)

            # Enregistrer la synchronisation
            sync_info = self.change_tracker.record_sync(content_items)

            # Mettre à jour les statistiques
            self.stats["end_time"] = datetime.now()
            self.stats["total_content_items"] = len(content_items)
            self.stats["total_attachments"] = sum(len(item.attachments) for item in content_items)
            self.stats["changed_content_items"] = len(changed_items)
            self.stats["processing_time_seconds"] = (
                self.stats["end_time"] - self.stats["start_time"]
            ).total_seconds()

            self.logger.info(f"Fin de la synchronisation. Statistiques: {self.stats}")

            return {**self.stats, **sync_info}
        except AuthenticationError as e:
            self.logger.error(f"Erreur d'authentification: {e}")
            self.stats["errors"] += 1
            raise
        except RateLimitExceededError as e:
            self.logger.error(f"Limite de taux d'appels API dépassée: {e}")
            self.stats["errors"] += 1
            raise
        except APIError as e:
            self.logger.error(f"Erreur API: {e}")
            self.stats["errors"] += 1
            raise
        except Exception as e:
            self.logger.error(f"Erreur inattendue lors de la synchronisation: {e}")
            self.stats["errors"] += 1
            raise

    async def _retrieve_content(self) -> List[ContentItem]:
        """Recherche et récupère les contenus selon les critères spécifiés."""
        try:
            self.logger.info(f"Recherche de contenu avec les critères: {self.criteria}")

            # Rechercher et récupérer les contenus
            content_items = await self.content_retriever.search_and_retrieve(
                self.criteria,
                process_attachments=self.criteria.include_attachments
            )

            self.logger.info(f"Récupération de {len(content_items)} éléments de contenu terminée")

            return content_items
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du contenu: {e}")
            self.stats["errors"] += 1
            raise

    async def _process_changed_content(self, content_items: List[ContentItem]) -> List[ContentItem]:
        """Traite les contenus qui ont changé depuis la dernière synchronisation."""
        changed_items = []

        for item in content_items:
            try:
                # Vérifier si le contenu a changé
                if self.change_tracker.has_content_changed(item):
                    self.logger.info(f"Contenu modifié détecté: {item.id} - {item.title}")
                    changed_items.append(item)
                    self.stats["changed_content_items"] += 1

                    # Stocker le contenu modifié
                    await self._store_content(item)

                    # Vérifier les pièces jointes modifiées
                    for attachment in item.attachments:
                        if self.change_tracker.has_attachment_changed(attachment):
                            self.logger.info(
                                f"Pièce jointe modifiée détectée: {attachment.id} - {attachment.file_name}"
                            )
                            self.stats["changed_attachments"] += 1

                            # Stocker la pièce jointe modifiée si elle est incluse dans les critères
                            if self.storage_config.include_attachments:
                                await self._store_attachment(item.id, attachment)
            except Exception as e:
                self.logger.error(f"Erreur lors du traitement du contenu {item.id}: {e}")
                self.stats["errors"] += 1
                # Continuer avec le contenu suivant

        self.logger.info(
            f"Traitement terminé. {len(changed_items)} contenus modifiés, "
            f"{self.stats['changed_attachments']} pièces jointes modifiées, "
            f"{self.stats['stored_content_items']} contenus stockés, "
            f"{self.stats['stored_attachments']} pièces jointes stockées."
        )

        return changed_items

    async def _store_content(self, content_item: ContentItem) -> str:
        """Stocke un contenu dans le système de stockage configuré."""
        try:
            # Convertir l'objet ContentItem en dictionnaire pour le stockage
            content_data = content_item.dict(exclude={'attachments'})

            # Ajouter des métadonnées supplémentaires
            content_data["storage_timestamp"] = datetime.now().isoformat()
            content_data["storage_type"] = self.storage_config.storage_type

            # Stocker le contenu
            storage_path = await self.storage.save_content(content_item.id, content_data)

            self.logger.info(f"Contenu {content_item.id} stocké dans {storage_path}")
            self.stats["stored_content_items"] += 1

            return storage_path
        except Exception as e:
            self.logger.error(f"Erreur lors du stockage du contenu {content_item.id}: {e}")
            self.stats["errors"] += 1
            raise

    async def _store_attachment(self, content_id: str, attachment: AttachmentDetail) -> Optional[str]:
        """Stocke une pièce jointe dans le système de stockage configuré."""
        try:
            # Vérifier si l'extension de la pièce jointe est dans la liste des extensions à convertir
            _, ext = os.path.splitext(attachment.file_name.lower())
            if ext not in self.storage_config.attachment_extensions_to_convert:
                self.logger.info(f"Extension {ext} non incluse dans les extensions à convertir, ignorée")
                return None

            # Vérifier la taille de la pièce jointe
            max_size_bytes = self.storage_config.max_attachment_size_mb * 1024 * 1024
            if attachment.file_size > max_size_bytes:
                self.logger.warning(
                    f"Pièce jointe {attachment.id} trop volumineuse "
                    f"({attachment.file_size} octets > {max_size_bytes} octets), ignorée"
                )
                return None

            # Télécharger la pièce jointe
            attachment_data = await self.client.download_attachment(attachment)

            # Stocker la pièce jointe
            storage_path = await self.storage.save_attachment(
                content_id,
                attachment.id,
                attachment.file_name,
                attachment_data
            )

            self.logger.info(f"Pièce jointe {attachment.id} stockée dans {storage_path}")
            self.stats["stored_attachments"] += 1

            return storage_path
        except Exception as e:
            self.logger.error(f"Erreur lors du stockage de la pièce jointe {attachment.id}: {e}")
            self.stats["errors"] += 1
            return None

    async def get_sync_status(self) -> Dict[str, Any]:
        """Récupère le statut actuel de la synchronisation."""
        # Récupérer les informations de la dernière synchronisation
        last_sync = self.change_tracker.get_last_sync_info()

        status = {
            "is_running": self.stats["start_time"] is not None and self.stats["end_time"] is None,
            "last_sync": last_sync,
            "current_stats": self.stats
        }

        return status