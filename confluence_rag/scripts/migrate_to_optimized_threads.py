#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de migration pour passer aux pools de threads optimisés.
"""

import os
import re
import argparse
import logging
from pathlib import Path
from typing import List, Tuple


def setup_logging(verbose: bool = False):
    """Configure le logging."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def find_python_files(directory: str) -> List[Path]:
    """Trouve tous les fichiers Python dans un répertoire."""
    directory_path = Path(directory)
    return list(directory_path.rglob("*.py"))


def analyze_file(file_path: Path) -> List[Tuple[int, str, str]]:
    """
    Analyse un fichier pour trouver les patterns à migrer.
    
    Returns:
        Liste de tuples (ligne, ancien_code, nouveau_code)
    """
    patterns_to_migrate = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            # Pattern 1: asyncio.to_thread
            if 'asyncio.to_thread(' in line:
                patterns_to_migrate.append((
                    line_num,
                    line.strip(),
                    "# MIGRATION: Remplacer asyncio.to_thread par thread_pool_manager.run_in_*_pool"
                ))
            
            # Pattern 2: ThreadPoolExecutor local
            if 'ThreadPoolExecutor(' in line and 'self.executor' in line:
                patterns_to_migrate.append((
                    line_num,
                    line.strip(),
                    "# MIGRATION: Remplacer ThreadPoolExecutor local par gestionnaire centralisé"
                ))
            
            # Pattern 3: loop.run_in_executor avec None
            if 'loop.run_in_executor(None,' in line or 'run_in_executor(None,' in line:
                patterns_to_migrate.append((
                    line_num,
                    line.strip(),
                    "# MIGRATION: Remplacer run_in_executor(None) par thread_pool_manager.run_in_io_pool"
                ))
            
            # Pattern 4: loop.run_in_executor avec self.executor
            if 'run_in_executor(self.executor,' in line:
                patterns_to_migrate.append((
                    line_num,
                    line.strip(),
                    "# MIGRATION: Remplacer run_in_executor(self.executor) par thread_pool_manager.run_in_document_pool"
                ))
    
    except Exception as e:
        logging.error(f"Erreur lors de l'analyse de {file_path}: {e}")
    
    return patterns_to_migrate


def generate_migration_suggestions(file_path: Path, patterns: List[Tuple[int, str, str]]) -> str:
    """Génère des suggestions de migration pour un fichier."""
    if not patterns:
        return ""
    
    suggestions = [
        f"\n=== Fichier: {file_path} ===",
        f"Nombre de patterns trouvés: {len(patterns)}\n"
    ]
    
    for line_num, old_code, suggestion in patterns:
        suggestions.extend([
            f"Ligne {line_num}:",
            f"  Ancien: {old_code}",
            f"  Action: {suggestion}",
            ""
        ])
    
    # Ajouter des exemples de migration
    suggestions.extend([
        "Exemples de migration:",
        "",
        "1. asyncio.to_thread:",
        "   Ancien: content = await asyncio.to_thread(self.client.get_page_by_id, page_id)",
        "   Nouveau: content = await self.thread_pool_manager.run_in_api_pool(self.client.get_page_by_id, page_id)",
        "",
        "2. ThreadPoolExecutor local:",
        "   Ancien: self.executor = ThreadPoolExecutor(max_workers=5)",
        "   Nouveau: self.thread_pool_manager = get_thread_pool_manager()",
        "",
        "3. run_in_executor avec None:",
        "   Ancien: await loop.run_in_executor(None, self._read_file, path)",
        "   Nouveau: await self.thread_pool_manager.run_in_io_pool(self._read_file, path)",
        "",
        "4. run_in_executor avec executor:",
        "   Ancien: await loop.run_in_executor(self.executor, self._extract_text, data)",
        "   Nouveau: await self.thread_pool_manager.run_in_document_pool(self._extract_text, data)",
        ""
    ])
    
    return "\n".join(suggestions)


def create_migration_config_example() -> str:
    """Crée un exemple de configuration pour les pools de threads."""
    return """
# Exemple de configuration des pools de threads optimisés

from confluence_rag.config import ThreadPoolConfig, ProcessingConfig

# Configuration des pools de threads
thread_pool_config = ThreadPoolConfig(
    io_thread_workers=8,           # Threads pour les opérations I/O
    document_processing_workers=4,  # Threads pour le traitement de documents
    api_thread_workers=3,          # Threads pour les appels API
    thread_name_prefix="MonApp",
    max_queue_size=100
)

# Configuration de traitement
processing_config = ProcessingConfig(
    max_parallel_downloads=8,
    thread_pool_config=thread_pool_config
)

# Variables d'environnement correspondantes:
# IO_THREAD_WORKERS=8
# DOCUMENT_PROCESSING_WORKERS=4
# API_THREAD_WORKERS=3
# THREAD_NAME_PREFIX=MonApp
# THREAD_POOL_MAX_QUEUE_SIZE=100
# MAX_PARALLEL_DOWNLOADS=8
"""


def main():
    """Fonction principale du script de migration."""
    parser = argparse.ArgumentParser(
        description="Script de migration vers les pools de threads optimisés"
    )
    parser.add_argument(
        "directory",
        help="Répertoire à analyser pour la migration"
    )
    parser.add_argument(
        "--output",
        "-o",
        default="migration_report.txt",
        help="Fichier de sortie pour le rapport de migration"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Mode verbeux"
    )
    
    args = parser.parse_args()
    
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info(f"Analyse du répertoire: {args.directory}")
    
    # Vérifier que le répertoire existe
    if not os.path.exists(args.directory):
        logger.error(f"Le répertoire {args.directory} n'existe pas")
        return 1
    
    # Trouver tous les fichiers Python
    python_files = find_python_files(args.directory)
    logger.info(f"Trouvé {len(python_files)} fichiers Python")
    
    # Analyser chaque fichier
    all_suggestions = []
    total_patterns = 0
    
    for file_path in python_files:
        logger.debug(f"Analyse de {file_path}")
        patterns = analyze_file(file_path)
        
        if patterns:
            total_patterns += len(patterns)
            suggestions = generate_migration_suggestions(file_path, patterns)
            all_suggestions.append(suggestions)
    
    # Générer le rapport
    logger.info(f"Génération du rapport de migration: {args.output}")
    
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write("RAPPORT DE MIGRATION - POOLS DE THREADS OPTIMISÉS\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Répertoire analysé: {args.directory}\n")
        f.write(f"Fichiers Python trouvés: {len(python_files)}\n")
        f.write(f"Patterns à migrer trouvés: {total_patterns}\n\n")
        
        if total_patterns == 0:
            f.write("✅ Aucune migration nécessaire trouvée!\n")
        else:
            f.write("📋 DÉTAILS DES MIGRATIONS NÉCESSAIRES:\n")
            f.write("-" * 40 + "\n")
            
            for suggestions in all_suggestions:
                f.write(suggestions)
                f.write("\n" + "-" * 60 + "\n")
        
        # Ajouter l'exemple de configuration
        f.write("\n📝 EXEMPLE DE CONFIGURATION:\n")
        f.write("-" * 30 + "\n")
        f.write(create_migration_config_example())
        
        # Ajouter les étapes de migration
        f.write("\n🚀 ÉTAPES DE MIGRATION:\n")
        f.write("-" * 20 + "\n")
        f.write("""
1. Installer la nouvelle version du système RAG Confluence
2. Ajouter l'import du gestionnaire de pools de threads:
   from confluence_rag.thread_pool_manager import get_thread_pool_manager

3. Remplacer les ThreadPoolExecutor locaux par le gestionnaire centralisé:
   # Ancien
   self.executor = ThreadPoolExecutor(max_workers=5)
   
   # Nouveau
   self.thread_pool_manager = get_thread_pool_manager()

4. Remplacer les appels asyncio.to_thread et run_in_executor:
   # Voir les exemples spécifiques dans le rapport ci-dessus

5. Configurer les pools de threads selon vos besoins:
   # Voir l'exemple de configuration ci-dessus

6. Tester les performances et ajuster la configuration si nécessaire

7. Optionnel: Utiliser le décorateur @thread_pool_executor pour automatiser
""")
    
    logger.info(f"Rapport généré: {args.output}")
    
    if total_patterns > 0:
        logger.warning(f"⚠️  {total_patterns} patterns nécessitent une migration")
        logger.info("Consultez le rapport pour les détails de migration")
        return 1
    else:
        logger.info("✅ Aucune migration nécessaire!")
        return 0


if __name__ == "__main__":
    exit(main())
