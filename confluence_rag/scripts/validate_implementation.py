#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de validation de l'implémentation des optimisations de performance.
"""

import sys
import importlib
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def validate_imports():
    """Valide que tous les modules peuvent être importés."""
    logger = logging.getLogger(__name__)
    
    modules_to_test = [
        'confluence_rag.config',
        'confluence_rag.client',
        'confluence_rag.models',
        'confluence_rag.exceptions',
    ]
    
    logger.info("🔍 Validation des imports...")
    
    for module_name in modules_to_test:
        try:
            module = importlib.import_module(module_name)
            logger.info(f"  ✅ {module_name}")
        except ImportError as e:
            logger.error(f"  ❌ {module_name}: {e}")
            return False
    
    return True

def validate_config():
    """Valide la configuration avec les nouveaux paramètres."""
    logger = logging.getLogger(__name__)
    
    try:
        from confluence_rag.config import ConfluenceConfig
        
        logger.info("🔧 Validation de la configuration...")
        
        # Test de création avec paramètres par défaut
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token="test_token"
        )
        
        # Vérifier les nouveaux attributs
        required_attrs = [
            'enable_parallel_pagination',
            'max_parallel_requests',
            'parallel_pagination_threshold'
        ]
        
        for attr in required_attrs:
            if hasattr(config, attr):
                value = getattr(config, attr)
                logger.info(f"  ✅ {attr}: {value}")
            else:
                logger.error(f"  ❌ Attribut manquant: {attr}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur de configuration: {e}")
        return False

def validate_client_methods():
    """Valide que le client a les nouvelles méthodes."""
    logger = logging.getLogger(__name__)
    
    try:
        from confluence_rag.client import ConfluenceClient
        from confluence_rag.config import ConfluenceConfig
        
        logger.info("🚀 Validation du client...")
        
        # Créer une instance de test
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token="test_token"
        )
        client = ConfluenceClient(config)
        
        # Vérifier les nouvelles méthodes
        required_methods = [
            '_get_session',
            '_close_session',
            '_make_cql_request',
            '_estimate_total_results',
            '_calculate_parallel_pages',
            '_fetch_page_parallel',
            '_search_content_parallel',
            '_search_content_sequential',
            'close'
        ]
        
        for method_name in required_methods:
            if hasattr(client, method_name):
                logger.info(f"  ✅ {method_name}")
            else:
                logger.error(f"  ❌ Méthode manquante: {method_name}")
                return False
        
        # Vérifier le support du context manager
        if hasattr(client, '__aenter__') and hasattr(client, '__aexit__'):
            logger.info("  ✅ Support context manager (async with)")
        else:
            logger.error("  ❌ Support context manager manquant")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur de validation du client: {e}")
        return False

def validate_files_structure():
    """Valide que tous les fichiers nécessaires existent."""
    logger = logging.getLogger(__name__)
    
    logger.info("📁 Validation de la structure des fichiers...")
    
    required_files = [
        # Documentation
        'confluence_rag/docs/PERFORMANCE_OPTIMIZATION.md',
        'confluence_rag/docs/PARALLEL_PAGINATION_GUIDE.md',
        'confluence_rag/docs/NOUVELLES_FONCTIONNALITES.md',
        
        # Exemples
        'confluence_rag/examples/parallel_pagination_example.py',
        'confluence_rag/examples/optimized_search_example.py',
        
        # Tests
        'confluence_rag/tests/test_parallel_pagination.py',
        'confluence_rag/tests/test_performance_optimization.py',
        
        # Benchmarks
        'confluence_rag/benchmarks/parallel_pagination_benchmark.py',
        'confluence_rag/benchmarks/search_performance_benchmark.py',
        
        # Scripts de test
        'test_parallel_pagination.py',
        'test_optimized_client.py',
        
        # Configuration
        '.env.example',
        'CHANGELOG.md',
        'README.md'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            logger.info(f"  ✅ {file_path}")
        else:
            logger.error(f"  ❌ Fichier manquant: {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def validate_env_example():
    """Valide que .env.example contient les nouvelles variables."""
    logger = logging.getLogger(__name__)
    
    logger.info("⚙️ Validation du fichier .env.example...")
    
    try:
        with open('.env.example', 'r') as f:
            content = f.read()
        
        required_vars = [
            'ENABLE_PARALLEL_PAGINATION',
            'MAX_PARALLEL_REQUESTS',
            'PARALLEL_PAGINATION_THRESHOLD'
        ]
        
        for var in required_vars:
            if var in content:
                logger.info(f"  ✅ {var}")
            else:
                logger.error(f"  ❌ Variable manquante: {var}")
                return False
        
        return True
        
    except FileNotFoundError:
        logger.error("❌ Fichier .env.example non trouvé")
        return False
    except Exception as e:
        logger.error(f"❌ Erreur lors de la lecture de .env.example: {e}")
        return False

def main():
    """Fonction principale de validation."""
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Démarrage de la validation de l'implémentation")
    logger.info("="*60)
    
    validations = [
        ("Imports", validate_imports),
        ("Configuration", validate_config),
        ("Méthodes du client", validate_client_methods),
        ("Structure des fichiers", validate_files_structure),
        ("Variables d'environnement", validate_env_example),
    ]
    
    results = []
    
    for name, validation_func in validations:
        logger.info(f"\n📋 {name}...")
        try:
            result = validation_func()
            results.append((name, result))
            
            if result:
                logger.info(f"✅ {name}: SUCCÈS")
            else:
                logger.error(f"❌ {name}: ÉCHEC")
                
        except Exception as e:
            logger.error(f"❌ {name}: ERREUR - {e}")
            results.append((name, False))
    
    # Résumé final
    logger.info("\n" + "="*60)
    logger.info("📊 RÉSUMÉ DE LA VALIDATION")
    logger.info("="*60)
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for name, result in results:
        status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
        logger.info(f"{name}: {status}")
    
    logger.info(f"\nRésultat global: {success_count}/{total_count} validations réussies")
    
    if success_count == total_count:
        logger.info("🎉 TOUTES LES VALIDATIONS ONT RÉUSSI !")
        logger.info("✅ L'implémentation de la pagination parallèle est complète et fonctionnelle.")
        return 0
    else:
        logger.error(f"❌ {total_count - success_count} validation(s) ont échoué.")
        logger.error("🔧 Veuillez corriger les problèmes identifiés.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
