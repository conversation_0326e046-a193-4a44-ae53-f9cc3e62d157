#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour la sécurité des logs.
"""

import unittest
import logging
import io
from unittest.mock import patch, MagicMock

from confluence_rag.client import ConfluenceClient
from confluence_rag.logging_utils import SecurityFilter
from confluence_rag.config import ConfluenceConfig


class TestLogSecurity(unittest.TestCase):
    """Tests pour la sécurisation des logs."""

    def setUp(self):
        """Configuration des tests."""
        self.security_filter = SecurityFilter()

    def test_sanitize_pat_token(self):
        """Test du masquage des Personal Access Tokens."""
        test_cases = [
            ("Error with token ABC123XYZ456789012345", "Error with token ***TOKEN***"),
            ("Authentication failed: ABCDEFGHIJKLMNOPQRSTUVWXYZ123456", "Authentication failed: ***TOKEN***"),
            ("Multiple tokens: ABC123 and XYZ789012345678901234", "Multiple tokens: ***TOKEN*** and ***TOKEN***"),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                self.assertNotIn("ABC123XYZ456789012345", result)
                self.assertIn("***TOKEN***", result)

    def test_sanitize_api_token_with_email(self):
        """Test du masquage des API tokens avec email."""
        test_cases = [
            ("Auth: <EMAIL>:ABCD1234567890123456", "Auth: ***EMAIL:TOKEN***"),
            ("<NAME_EMAIL>:XYZ9876543210987654", "Failed login ***EMAIL:TOKEN***"),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                self.assertNotIn("<EMAIL>:ABCD1234567890123456", result)
                self.assertIn("***EMAIL:TOKEN***", result)

    def test_sanitize_authorization_headers(self):
        """Test du masquage des headers Authorization."""
        test_cases = [
            ("Authorization: Bearer ABC123XYZ456", "Authorization: Bearer ***TOKEN***"),
            ("Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==", "Authorization: Basic ***TOKEN***"),
            ("Request failed: Authorization: Bearer TOKEN123", "Request failed: Authorization: Bearer ***TOKEN***"),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                self.assertIn("Authorization:", result)
                self.assertIn("***TOKEN***", result)
                self.assertNotIn("ABC123XYZ456", result)
                self.assertNotIn("QWxhZGRpbjpvcGVuIHNlc2FtZQ==", result)

    def test_sanitize_url_passwords(self):
        """Test du masquage des mots de passe dans les URLs."""
        test_cases = [
            ("https://user:<EMAIL>/api", "https://***:***@example.com/api"),
            ("Connection to ftp://admin:<EMAIL> failed", "Connection to ftp://***:***@server.com failed"),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                self.assertNotIn("password", result)
                self.assertNotIn("secret123", result)
                self.assertIn("***:***", result)

    def test_sanitize_url_parameters(self):
        """Test du masquage des paramètres sensibles dans les URLs."""
        test_cases = [
            ("GET /api?token=ABC123&user=john", ["?token=***", "user=john"]),
            ("Request: /auth?password=secret&key=XYZ789", ["?password=***", "&key=***"]),
            ("URL: /api?secret=hidden&public=visible", ["?secret=***", "public=visible"]),
        ]

        for original, expected_parts in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                for part in expected_parts:
                    self.assertIn(part, result)
                # Vérifier que les valeurs sensibles ont été supprimées
                self.assertNotIn("ABC123", result)
                self.assertNotIn("XYZ789", result)
                self.assertNotIn("hidden", result)
                # Vérifier que "secret" n'apparaît pas comme valeur (mais peut apparaître comme nom de paramètre)
                if "secret=hidden" in original:
                    self.assertNotIn("secret=hidden", result)

    def test_sanitize_json_keys(self):
        """Test du masquage des clés sensibles dans le JSON."""
        test_cases = [
            ('{"api_token": "ABC123", "user": "john"}', '{"api_token": "***", "user": "john"}'),
            ('Config: "pat_token": "XYZ789"', 'Config: "pat_token": "***"'),
            ('{"password": "secret", "public": "data"}', '{"password": "***", "public": "data"}'),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                self.assertIn('"***"', result)
                self.assertNotIn("ABC123", result)
                self.assertNotIn("XYZ789", result)
                self.assertNotIn("secret", result)

    def test_confluence_client_sanitize_error(self):
        """Test de la méthode de nettoyage du ConfluenceClient."""
        test_error = "Authentication failed with token ABC123XYZ456789012345"
        sanitized = ConfluenceClient._sanitize_error_message(test_error)

        self.assertNotIn("ABC123XYZ456789012345", sanitized)
        self.assertIn("***TOKEN***", sanitized)
        self.assertIn("Authentication failed", sanitized)

    def test_security_filter_with_log_record(self):
        """Test du filtre de sécurité avec un vrai LogRecord."""
        # Créer un logger de test
        logger = logging.getLogger("test_security")

        # Créer un handler en mémoire pour capturer les logs
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.addFilter(self.security_filter)

        logger.addHandler(handler)
        logger.setLevel(logging.INFO)

        # Logger un message avec des informations sensibles
        sensitive_message = "Error: token ABC123XYZ456789012345 failed"
        logger.info(sensitive_message)

        # Vérifier que le message a été nettoyé
        log_output = log_stream.getvalue()
        self.assertNotIn("ABC123XYZ456789012345", log_output)
        self.assertIn("***TOKEN***", log_output)

    def test_security_filter_with_args(self):
        """Test du filtre de sécurité avec des arguments de message."""
        # Créer un LogRecord avec des arguments
        record = logging.LogRecord(
            name="test",
            level=logging.ERROR,
            pathname="",
            lineno=0,
            msg="Error with token %s and user %s",
            args=("ABC123XYZ456789012345", "john"),
            exc_info=None
        )

        # Appliquer le filtre
        self.security_filter.filter(record)

        # Vérifier que les arguments ont été nettoyés
        self.assertNotIn("ABC123XYZ456789012345", str(record.args))
        self.assertIn("***TOKEN***", str(record.args))
        self.assertEqual(record.args[1], "john")  # L'utilisateur ne doit pas être modifié

    def test_preserve_non_sensitive_data(self):
        """Test que les données non sensibles sont préservées."""
        test_cases = [
            "Normal log message without sensitive data",
            "User john logged in successfully",
            "Processing 100 items in 5.2 seconds",
            "Configuration loaded from /path/to/config.json",
        ]

        for original in test_cases:
            with self.subTest(original=original):
                result = self.security_filter._sanitize_message(original)
                self.assertEqual(original, result)

    def test_complex_mixed_content(self):
        """Test avec du contenu mixte complexe."""
        original = (
            "Request failed: POST https://user:<EMAIL>/auth "
            "with headers {'Authorization': 'Bearer ABC123XYZ456789012345'} "
            "and payload {\"api_token\": \"SECRET789\", \"user\": \"john\"}"
        )

        result = self.security_filter._sanitize_message(original)

        # Vérifier que toutes les informations sensibles sont masquées
        self.assertNotIn("pass", result)
        self.assertNotIn("ABC123XYZ456789012345", result)
        self.assertNotIn("SECRET789", result)

        # Vérifier que les informations non sensibles sont préservées
        self.assertIn("Request failed", result)
        self.assertIn("POST", result)
        self.assertIn("api.example.com", result)
        self.assertIn("john", result)


if __name__ == '__main__':
    unittest.main()
