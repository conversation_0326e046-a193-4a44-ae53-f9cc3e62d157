#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour la pagination parallèle du client Confluence.
"""

import asyncio
import pytest
import logging
from unittest.mock import AsyncMock, MagicMock, patch
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

# Configuration du logging pour les tests
logging.basicConfig(level=logging.INFO)

class TestParallelPagination:
    """Tests pour la pagination parallèle."""

    @pytest.fixture
    def mock_config(self):
        """Configuration mock avec pagination parallèle activée."""
        config = MagicMock(spec=ConfluenceConfig)
        config.url = "https://test.atlassian.net"
        config.pat_token = "test_token"
        config.api_token = None
        config.username = None
        config.timeout = 30
        config.retry_config = MagicMock()
        config.circuit_breaker_config = MagicMock()
        config.enable_parallel_pagination = True
        config.max_parallel_requests = 3
        config.parallel_pagination_threshold = 100
        return config

    @pytest.fixture
    def search_criteria_large(self):
        """Critères de recherche pour déclencher la pagination parallèle."""
        return SearchCriteria(
            spaces=["TEST"],
            types=["page"],
            max_results=300  # Au-dessus du seuil
        )

    @pytest.fixture
    def search_criteria_small(self):
        """Critères de recherche pour la pagination séquentielle."""
        return SearchCriteria(
            spaces=["TEST"],
            types=["page"],
            max_results=50  # En-dessous du seuil
        )

    @pytest.fixture
    def mock_response_data(self):
        """Données de réponse mock pour les tests."""
        def create_response(start, limit):
            return {
                "results": [
                    {
                        "id": f"test_id_{start + i}",
                        "type": "page",
                        "status": "current",
                        "title": f"Test Page {start + i}",
                        "space": {"id": "1", "key": "TEST", "name": "Test Space", "type": "global"},
                        "version": {"number": 1},
                        "history": {
                            "createdDate": "2023-01-01T00:00:00.000Z",
                            "createdBy": {"accountId": "user1", "displayName": "Test User"},
                            "lastUpdated": {
                                "when": "2023-01-01T00:00:00.000Z",
                                "by": {"accountId": "user1", "displayName": "Test User"}
                            }
                        },
                        "body": {
                            "storage": {"value": f"<p>Content {start + i}</p>"},
                            "view": {"value": f"<p>Content {start + i}</p>"}
                        },
                        "metadata": {"labels": {"results": []}},
                        "ancestors": [],
                        "children": {"page": {"results": []}}
                    }
                    for i in range(min(limit, 10))  # Simuler une page de résultats
                ],
                "size": 300,  # Total estimé
                "start": start,
                "limit": limit
            }
        return create_response

    @pytest.mark.asyncio
    async def test_parallel_pagination_enabled(self, mock_config, search_criteria_large):
        """Test que la pagination parallèle est utilisée quand elle est activée."""
        client = ConfluenceClient(mock_config)
        
        with patch.object(client, '_search_content_parallel') as mock_parallel:
            mock_parallel.return_value = []
            
            await client.search_content(search_criteria_large)
            
            # Vérifier que la méthode parallèle a été appelée
            mock_parallel.assert_called_once_with(search_criteria_large)

    @pytest.mark.asyncio
    async def test_sequential_pagination_for_small_requests(self, mock_config, search_criteria_small):
        """Test que la pagination séquentielle est utilisée pour les petites requêtes."""
        client = ConfluenceClient(mock_config)
        
        with patch.object(client, '_search_content_sequential') as mock_sequential:
            mock_sequential.return_value = []
            
            await client.search_content(search_criteria_small)
            
            # Vérifier que la méthode séquentielle a été appelée
            mock_sequential.assert_called_once_with(search_criteria_small)

    @pytest.mark.asyncio
    async def test_parallel_pagination_disabled(self, mock_config, search_criteria_large):
        """Test que la pagination séquentielle est utilisée quand la parallèle est désactivée."""
        mock_config.enable_parallel_pagination = False
        client = ConfluenceClient(mock_config)
        
        with patch.object(client, '_search_content_sequential') as mock_sequential:
            mock_sequential.return_value = []
            
            await client.search_content(search_criteria_large)
            
            # Vérifier que la méthode séquentielle a été appelée même pour une grande requête
            mock_sequential.assert_called_once_with(search_criteria_large)

    @pytest.mark.asyncio
    async def test_estimate_total_results(self, mock_config, mock_response_data):
        """Test de l'estimation du nombre total de résultats."""
        client = ConfluenceClient(mock_config)
        
        with patch.object(client, '_make_cql_request') as mock_request:
            mock_request.return_value = mock_response_data(0, 1)
            
            total = await client._estimate_total_results("test cql")
            
            assert total == 300
            mock_request.assert_called_once_with(cql="test cql", start=0, limit=1)

    @pytest.mark.asyncio
    async def test_calculate_parallel_pages(self, mock_config):
        """Test du calcul des pages parallèles."""
        client = ConfluenceClient(mock_config)
        
        # Test avec 300 résultats totaux, 250 demandés, pages de 100
        pages = client._calculate_parallel_pages(300, 250, 100)
        
        expected_pages = [(0, 100), (100, 100), (200, 50)]
        assert pages == expected_pages

    @pytest.mark.asyncio
    async def test_fetch_page_parallel(self, mock_config, mock_response_data):
        """Test de récupération d'une page en parallèle."""
        client = ConfluenceClient(mock_config)
        
        with patch.object(client, '_make_cql_request') as mock_request:
            mock_request.return_value = mock_response_data(0, 10)
            
            results = await client._fetch_page_parallel("test cql", 0, 10, "expand")
            
            assert len(results) == 10
            assert results[0]["id"] == "test_id_0"
            mock_request.assert_called_once_with(cql="test cql", start=0, limit=10, expand="expand")

    @pytest.mark.asyncio
    async def test_parallel_pagination_with_semaphore(self, mock_config, search_criteria_large, mock_response_data):
        """Test que la pagination parallèle respecte les limites de concurrence."""
        client = ConfluenceClient(mock_config)
        
        call_count = 0
        
        async def mock_make_request(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            start = kwargs.get('start', 0)
            limit = kwargs.get('limit', 10)
            return mock_response_data(start, limit)
        
        with patch.object(client, '_make_cql_request', side_effect=mock_make_request):
            results = await client._search_content_parallel(search_criteria_large)
            
            # Vérifier que des résultats ont été obtenus
            assert len(results) > 0
            # Vérifier qu'au moins une estimation et plusieurs pages ont été appelées
            assert call_count > 1

    @pytest.mark.asyncio
    async def test_parallel_pagination_error_handling(self, mock_config, search_criteria_large):
        """Test de la gestion d'erreurs en pagination parallèle."""
        client = ConfluenceClient(mock_config)
        
        with patch.object(client, '_estimate_total_results', side_effect=Exception("Test error")):
            with patch.object(client, '_search_content_sequential') as mock_sequential:
                mock_sequential.return_value = []
                
                # La méthode devrait faire un fallback vers la pagination séquentielle
                await client._search_content_parallel(search_criteria_large)
                
                mock_sequential.assert_called_once_with(search_criteria_large)

    @pytest.mark.asyncio
    async def test_parallel_pagination_partial_failure(self, mock_config, search_criteria_large, mock_response_data):
        """Test de gestion des échecs partiels en pagination parallèle."""
        client = ConfluenceClient(mock_config)
        
        call_count = 0
        
        async def mock_make_request(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            # Première requête (estimation) réussit
            if call_count == 1:
                return {"size": 300}
            
            # Deuxième requête échoue
            elif call_count == 2:
                raise Exception("Network error")
            
            # Autres requêtes réussissent
            else:
                start = kwargs.get('start', 0)
                limit = kwargs.get('limit', 10)
                return mock_response_data(start, limit)
        
        with patch.object(client, '_make_cql_request', side_effect=mock_make_request):
            results = await client._search_content_parallel(search_criteria_large)
            
            # Même avec un échec partiel, on devrait avoir des résultats
            assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_threshold_configuration(self, mock_config):
        """Test de la configuration du seuil de pagination parallèle."""
        # Test avec seuil élevé
        mock_config.parallel_pagination_threshold = 500
        client = ConfluenceClient(mock_config)
        
        criteria = SearchCriteria(max_results=300)  # En-dessous du seuil
        
        with patch.object(client, '_search_content_sequential') as mock_sequential:
            mock_sequential.return_value = []
            
            await client.search_content(criteria)
            
            # Devrait utiliser la pagination séquentielle
            mock_sequential.assert_called_once()

    @pytest.mark.asyncio
    async def test_max_parallel_requests_limit(self, mock_config):
        """Test de la limite du nombre de requêtes parallèles."""
        mock_config.max_parallel_requests = 2
        client = ConfluenceClient(mock_config)
        
        # Créer plus de pages que la limite
        pages = [(0, 100), (100, 100), (200, 100), (300, 100)]
        
        # Le nombre de tâches concurrentes ne devrait pas dépasser max_parallel_requests
        max_concurrent = min(mock_config.max_parallel_requests, len(pages))
        assert max_concurrent == 2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
