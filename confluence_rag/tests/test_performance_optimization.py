#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de performance pour comparer l'ancienne et la nouvelle approche de search_content().
"""

import asyncio
import time
import pytest
import logging
from unittest.mock import AsyncMock, MagicMock, patch
from confluence_rag.client import ConfluenceClient
from confluence_rag.config import ConfluenceConfig, SearchCriteria

# Configuration du logging pour les tests
logging.basicConfig(level=logging.INFO)

class TestPerformanceOptimization:
    """Tests de performance pour les optimisations du client Confluence."""

    @pytest.fixture
    def mock_config(self):
        """Configuration mock pour les tests."""
        config = MagicMock(spec=ConfluenceConfig)
        config.url = "https://test.atlassian.net"
        config.pat_token = "test_token"
        config.api_token = None
        config.username = None
        config.timeout = 30
        config.retry_config = MagicMock()
        config.circuit_breaker_config = MagicMock()
        return config

    @pytest.fixture
    def search_criteria(self):
        """Critères de recherche pour les tests."""
        return SearchCriteria(
            spaces=["TEST"],
            types=["page"],
            max_results=50
        )

    @pytest.fixture
    def mock_response_data(self):
        """Données de réponse mock pour les tests."""
        return {
            "results": [
                {
                    "id": f"test_id_{i}",
                    "type": "page",
                    "status": "current",
                    "title": f"Test Page {i}",
                    "space": {"id": "1", "key": "TEST", "name": "Test Space", "type": "global"},
                    "version": {"number": 1},
                    "history": {
                        "createdDate": "2023-01-01T00:00:00.000Z",
                        "createdBy": {"accountId": "user1", "displayName": "Test User"},
                        "lastUpdated": {
                            "when": "2023-01-01T00:00:00.000Z",
                            "by": {"accountId": "user1", "displayName": "Test User"}
                        }
                    },
                    "body": {
                        "storage": {"value": f"<p>Content {i}</p>"},
                        "view": {"value": f"<p>Content {i}</p>"}
                    },
                    "metadata": {"labels": {"results": []}},
                    "ancestors": [],
                    "children": {"page": {"results": []}}
                }
                for i in range(10)
            ],
            "size": 10,
            "start": 0,
            "limit": 10
        }

    @pytest.mark.asyncio
    async def test_optimized_session_creation(self, mock_config):
        """Test que la session HTTP est créée correctement."""
        client = ConfluenceClient(mock_config)
        
        # Vérifier que la session n'est pas créée à l'initialisation
        assert client._session is None
        
        # Créer la session
        session = await client._get_session()
        
        # Vérifier que la session est créée avec les bons paramètres
        assert session is not None
        assert not session.closed
        assert "Authorization" in session._default_headers
        assert session._default_headers["Authorization"] == "Bearer test_token"
        
        # Nettoyer
        await client.close()

    @pytest.mark.asyncio
    async def test_session_reuse(self, mock_config):
        """Test que la session HTTP est réutilisée entre les appels."""
        client = ConfluenceClient(mock_config)
        
        # Obtenir la session deux fois
        session1 = await client._get_session()
        session2 = await client._get_session()
        
        # Vérifier que c'est la même instance
        assert session1 is session2
        
        # Nettoyer
        await client.close()

    @pytest.mark.asyncio
    async def test_context_manager_cleanup(self, mock_config):
        """Test que le context manager ferme proprement la session."""
        async with ConfluenceClient(mock_config) as client:
            session = await client._get_session()
            assert not session.closed
        
        # Après la sortie du context manager, la session doit être fermée
        assert session.closed

    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession.get')
    async def test_optimized_search_content(self, mock_get, mock_config, search_criteria, mock_response_data):
        """Test de la méthode search_content optimisée."""
        # Configurer le mock de la réponse HTTP
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_response_data
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with ConfluenceClient(mock_config) as client:
            # Mesurer le temps d'exécution
            start_time = time.time()
            results = await client.search_content(search_criteria)
            end_time = time.time()
            
            # Vérifier les résultats
            assert len(results) == 10
            assert all(result.id.startswith("test_id_") for result in results)
            
            # Vérifier que l'appel HTTP a été fait
            mock_get.assert_called_once()
            
            # Log du temps d'exécution pour analyse
            execution_time = end_time - start_time
            logging.info(f"Temps d'exécution de la recherche optimisée: {execution_time:.4f}s")

    @pytest.mark.asyncio
    @patch('aiohttp.ClientSession.get')
    async def test_multiple_searches_performance(self, mock_get, mock_config, mock_response_data):
        """Test de performance avec plusieurs recherches consécutives."""
        # Configurer le mock
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_response_data
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with ConfluenceClient(mock_config) as client:
            # Effectuer plusieurs recherches
            search_criteria_list = [
                SearchCriteria(spaces=["TEST1"], max_results=10),
                SearchCriteria(spaces=["TEST2"], max_results=10),
                SearchCriteria(spaces=["TEST3"], max_results=10),
            ]
            
            start_time = time.time()
            
            all_results = []
            for criteria in search_criteria_list:
                results = await client.search_content(criteria)
                all_results.extend(results)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Vérifier les résultats
            assert len(all_results) == 30  # 3 recherches × 10 résultats
            
            # Vérifier que la session a été réutilisée (3 appels avec la même session)
            assert mock_get.call_count == 3
            
            logging.info(f"Temps d'exécution pour 3 recherches: {execution_time:.4f}s")

    @pytest.mark.asyncio
    async def test_error_handling_in_optimized_method(self, mock_config, search_criteria):
        """Test de la gestion d'erreurs dans la méthode optimisée."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Simuler une erreur d'authentification
            mock_response = AsyncMock()
            mock_response.status = 401
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with ConfluenceClient(mock_config) as client:
                with pytest.raises(Exception):  # AuthenticationError
                    await client.search_content(search_criteria)

    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, mock_config, search_criteria):
        """Test de la gestion des limites de taux."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Simuler une erreur de limite de taux
            mock_response = AsyncMock()
            mock_response.status = 429
            mock_response.headers = {"Retry-After": "60"}
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with ConfluenceClient(mock_config) as client:
                with pytest.raises(Exception):  # RateLimitExceededError
                    await client.search_content(search_criteria)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
