```mermaid
stateDiagram-v2
    [*] --> CLOSED
    CLOSED --> OPEN: Échecs consécutifs >= failure_threshold
    OPEN --> HALF_OPEN: Temps écoulé >= reset_timeout
    HALF_OPEN --> CLOSED: Succès consécutifs >= reset_threshold
    HALF_OPEN --> OPEN: Nouvel échec
    
    state CLOSED {
        [*] --> Normal
        Normal --> Échec: Erreur
        Échec --> Normal: Succès
        Échec --> Échec: <PERSON><PERSON><PERSON> (compteur++)
    }
    
    state OPEN {
        [*] --> Bloqué
        Bloqué --> Attente: CircuitOpenError
        Attente --> [*]: Temps écoulé
    }
    
    state HALF_OPEN {
        [*] --> Test
        Test --> Succès: Appel réussi
        Succès --> Succès: A<PERSON> réussi (compteur++)
        Succès --> [*]: Compteur >= reset_threshold
        Test --> Échec: Appel échoué
        Échec --> [*]
    }
```

Ce diagramme illustre les trois états du Circuit Breaker (CLOSED, OPEN, HALF_OPEN) et les transitions entre ces états. Il montre également le comportement interne de chaque état.
