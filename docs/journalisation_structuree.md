# Journalisation Structurée avec Identifiants de Corrélation

Ce document décrit en détail le système de journalisation structurée avec identifiants de corrélation implémenté dans le projet RAG Confluence.

## Vue d'ensemble

La journalisation structurée est une approche qui consiste à formater les logs sous forme de données structurées (généralement JSON) plutôt que sous forme de texte brut. Cette approche facilite l'analyse, la recherche et le filtrage des logs, en particulier dans les systèmes distribués ou asynchrones.

Les identifiants de corrélation sont des identifiants uniques qui sont propagés à travers les différents composants d'un système pour suivre le flux d'exécution d'une opération. Ils permettent de regrouper tous les logs liés à une même opération, même si ces logs proviennent de différents composants ou services.

## Fonctionnalités

Notre implémentation offre les fonctionnalités suivantes :

1. **Format JSON structuré** : Tous les logs sont formatés en JSON avec des champs standardisés
2. **Identifiants de corrélation** : Chaque opération reçoit un identifiant unique (UUID)
3. **Propagation automatique** : Les identifiants sont propagés automatiquement à travers les appels asynchrones
4. **Contexte enrichi** : Les logs incluent des informations contextuelles détaillées
5. **Configuration flexible** : La journalisation structurée peut être activée/désactivée via la configuration

## Configuration

La journalisation structurée peut être configurée via le fichier `.env` :

```
# Configuration de logging
LOG_LEVEL=INFO                    # Niveau de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_FILE=confluence_rag.log       # Chemin du fichier de log
STRUCTURED_LOGGING=true           # Utiliser le format JSON structuré pour les logs
```

## Structure des logs

Chaque entrée de log structuré contient les champs suivants :

| Champ | Description | Exemple |
|-------|-------------|---------|
| `timestamp` | Date et heure de l'événement | `"2023-06-15T14:32:45.123456"` |
| `level` | Niveau de log | `"INFO"`, `"ERROR"`, etc. |
| `logger` | Nom du logger | `"confluence_rag.orchestrator"` |
| `correlation_id` | Identifiant de corrélation | `"550e8400-e29b-41d4-a716-446655440000"` |
| `message` | Message de log | `"Début de la synchronisation avec Confluence"` |
| `module` | Module Python | `"orchestrator"` |
| `function` | Fonction ou méthode | `"run"` |
| `line` | Numéro de ligne | `86` |
| `thread_id` | ID du thread | `123456` |
| `thread_name` | Nom du thread | `"MainThread"` |
| `process_id` | ID du processus | `654321` |
| `exception` | Informations d'exception (si présente) | `{"type": "ValueError", "message": "...", "traceback": "..."}` |

## Exemple de log structuré

```json
{
  "timestamp": "2023-06-15T14:32:45.123456",
  "level": "INFO",
  "logger": "confluence_rag.orchestrator",
  "correlation_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "Début de la synchronisation avec Confluence: https://example.atlassian.net/wiki",
  "module": "orchestrator",
  "function": "run",
  "line": 86,
  "thread_id": 123456,
  "thread_name": "MainThread",
  "process_id": 654321
}
```

## Utilisation dans le code

### Génération d'un nouvel identifiant de corrélation

Pour générer un nouvel identifiant de corrélation, utilisez le décorateur `with_correlation_id` :

```python
from confluence_rag.logging_utils import with_correlation_id, CorrelationContext

@with_correlation_id
async def ma_fonction():
    # Un nouvel identifiant est généré automatiquement
    correlation_id = CorrelationContext.get_correlation_id()
    logging.info(f"Fonction démarrée avec l'identifiant de corrélation: {correlation_id}")
    # ...
```

### Propagation d'un identifiant existant

Pour propager un identifiant existant, utilisez le décorateur `propagate_correlation_id` :

```python
from confluence_rag.logging_utils import propagate_correlation_id

@propagate_correlation_id
async def autre_fonction():
    # L'identifiant existant est propagé automatiquement
    correlation_id = CorrelationContext.get_correlation_id()
    logging.info(f"Fonction appelée avec l'identifiant de corrélation: {correlation_id}")
    # ...
```

### Accès à l'identifiant de corrélation

Pour accéder à l'identifiant de corrélation actuel :

```python
from confluence_rag.logging_utils import CorrelationContext

correlation_id = CorrelationContext.get_correlation_id()
if correlation_id:
    # Utiliser l'identifiant
    print(f"Identifiant de corrélation actuel: {correlation_id}")
else:
    print("Aucun identifiant de corrélation actif")
```

## Implémentation technique

### Classes principales

1. **CorrelationContext** : Gère le stockage et la récupération des identifiants de corrélation
2. **CorrelationIdFilter** : Filtre de logging qui ajoute l'identifiant aux enregistrements de log
3. **StructuredLogFormatter** : Formateur qui produit des logs au format JSON

### Contextes de stockage

Les identifiants de corrélation sont stockés dans deux contextes différents :

1. **Contexte de thread** : Pour les opérations synchrones
2. **Contexte de tâche asyncio** : Pour les opérations asynchrones

Cette double approche garantit que les identifiants sont correctement propagés dans tous les contextes d'exécution.

## Sécurité des logs

Le système intègre automatiquement un filtre de sécurité (`SecurityFilter`) qui protège les informations sensibles dans tous les logs :

- **Tokens d'authentification** : PAT tokens, API tokens masqués automatiquement
- **Headers Authorization** : Bearer et Basic tokens sécurisés
- **Mots de passe dans URLs** : Credentials dans les URLs masqués
- **Paramètres sensibles** : Tokens, passwords, secrets dans les paramètres d'URL
- **Données JSON** : Clés sensibles dans les structures JSON

Exemple de sécurisation automatique :
```json
{
  "timestamp": "2023-06-15T14:32:45.123456",
  "level": "ERROR",
  "message": "Erreur d'authentification avec token ***TOKEN***",
  "token": "***TOKEN***",
  "user": "john"
}
```

Pour plus de détails, consultez [Sécurité des logs](securite_logs.md).

## Bonnes pratiques

1. **Générer un identifiant au point d'entrée** : Utilisez `with_correlation_id` au point d'entrée de votre application
2. **Propager l'identifiant dans les fonctions appelées** : Utilisez `propagate_correlation_id` pour les fonctions appelées
3. **Inclure l'identifiant dans les logs importants** : Assurez-vous que tous les logs importants incluent l'identifiant
4. **Nettoyer l'identifiant après utilisation** : L'identifiant est automatiquement nettoyé à la fin de la fonction décorée
5. **Éviter les données sensibles** : Ne loggez jamais directement des tokens ou mots de passe (le filtre de sécurité les masquera automatiquement)

## Analyse des logs

Les logs structurés peuvent être analysés avec divers outils :

1. **Elasticsearch + Kibana** : Pour une analyse avancée et des visualisations
2. **Splunk** : Pour une analyse en temps réel et des alertes
3. **jq** : Pour une analyse en ligne de commande

Exemple d'analyse avec `jq` :

```bash
# Filtrer les logs par identifiant de corrélation
cat confluence_rag.log | jq 'select(.correlation_id == "550e8400-e29b-41d4-a716-446655440000")'

# Compter les erreurs par module
cat confluence_rag.log | jq 'select(.level == "ERROR") | .module' | sort | uniq -c
```

## Conclusion

La journalisation structurée avec identifiants de corrélation améliore considérablement la traçabilité et le débogage dans notre système RAG Confluence. Elle permet de suivre facilement le flux d'exécution à travers les différents composants, même dans un environnement asynchrone complexe.
