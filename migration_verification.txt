RAPPORT DE MIGRATION - POOLS DE THREADS OPTIMISÉS
============================================================

Répertoire analysé: confluence_rag
Fichiers Python trouvés: 23
Patterns à migrer trouvés: 9

📋 DÉTAILS DES MIGRATIONS NÉCESSAIRES:
----------------------------------------

=== Fichier: confluence_rag/scripts/migrate_to_optimized_threads.py ===
Nombre de patterns trouvés: 9

Ligne 46:
  Ancien: if 'asyncio.to_thread(' in line:
  Action: # MIGRATION: Remplacer asyncio.to_thread par thread_pool_manager.run_in_*_pool

Ligne 54:
  Ancien: if 'ThreadPoolExecutor(' in line and 'self.executor' in line:
  Action: # MIGRATION: Remplacer ThreadPoolExecutor local par gestionnaire centralisé

Ligne 62:
  Ancien: if 'loop.run_in_executor(None,' in line or 'run_in_executor(None,' in line:
  Action: # MIGRATION: Remplacer run_in_executor(None) par thread_pool_manager.run_in_io_pool

Ligne 70:
  Ancien: if 'run_in_executor(self.executor,' in line:
  Action: # MIGRATION: Remplacer run_in_executor(self.executor) par thread_pool_manager.run_in_document_pool

Ligne 106:
  Ancien: "   Ancien: content = await asyncio.to_thread(self.client.get_page_by_id, page_id)",
  Action: # MIGRATION: Remplacer asyncio.to_thread par thread_pool_manager.run_in_*_pool

Ligne 110:
  Ancien: "   Ancien: self.executor = ThreadPoolExecutor(max_workers=5)",
  Action: # MIGRATION: Remplacer ThreadPoolExecutor local par gestionnaire centralisé

Ligne 114:
  Ancien: "   Ancien: await loop.run_in_executor(None, self._read_file, path)",
  Action: # MIGRATION: Remplacer run_in_executor(None) par thread_pool_manager.run_in_io_pool

Ligne 118:
  Ancien: "   Ancien: await loop.run_in_executor(self.executor, self._extract_text, data)",
  Action: # MIGRATION: Remplacer run_in_executor(self.executor) par thread_pool_manager.run_in_document_pool

Ligne 244:
  Ancien: self.executor = ThreadPoolExecutor(max_workers=5)
  Action: # MIGRATION: Remplacer ThreadPoolExecutor local par gestionnaire centralisé

Exemples de migration:

1. asyncio.to_thread:
   Ancien: content = await asyncio.to_thread(self.client.get_page_by_id, page_id)
   Nouveau: content = await self.thread_pool_manager.run_in_api_pool(self.client.get_page_by_id, page_id)

2. ThreadPoolExecutor local:
   Ancien: self.executor = ThreadPoolExecutor(max_workers=5)
   Nouveau: self.thread_pool_manager = get_thread_pool_manager()

3. run_in_executor avec None:
   Ancien: await loop.run_in_executor(None, self._read_file, path)
   Nouveau: await self.thread_pool_manager.run_in_io_pool(self._read_file, path)

4. run_in_executor avec executor:
   Ancien: await loop.run_in_executor(self.executor, self._extract_text, data)
   Nouveau: await self.thread_pool_manager.run_in_document_pool(self._extract_text, data)

------------------------------------------------------------

📝 EXEMPLE DE CONFIGURATION:
------------------------------

# Exemple de configuration des pools de threads optimisés

from confluence_rag.config import ThreadPoolConfig, ProcessingConfig

# Configuration des pools de threads
thread_pool_config = ThreadPoolConfig(
    io_thread_workers=8,           # Threads pour les opérations I/O
    document_processing_workers=4,  # Threads pour le traitement de documents
    api_thread_workers=3,          # Threads pour les appels API
    thread_name_prefix="MonApp",
    max_queue_size=100
)

# Configuration de traitement
processing_config = ProcessingConfig(
    max_parallel_downloads=8,
    thread_pool_config=thread_pool_config
)

# Variables d'environnement correspondantes:
# IO_THREAD_WORKERS=8
# DOCUMENT_PROCESSING_WORKERS=4
# API_THREAD_WORKERS=3
# THREAD_NAME_PREFIX=MonApp
# THREAD_POOL_MAX_QUEUE_SIZE=100
# MAX_PARALLEL_DOWNLOADS=8

🚀 ÉTAPES DE MIGRATION:
--------------------

1. Installer la nouvelle version du système RAG Confluence
2. Ajouter l'import du gestionnaire de pools de threads:
   from confluence_rag.thread_pool_manager import get_thread_pool_manager

3. Remplacer les ThreadPoolExecutor locaux par le gestionnaire centralisé:
   # Ancien
   self.executor = ThreadPoolExecutor(max_workers=5)
   
   # Nouveau
   self.thread_pool_manager = get_thread_pool_manager()

4. Remplacer les appels asyncio.to_thread et run_in_executor:
   # Voir les exemples spécifiques dans le rapport ci-dessus

5. Configurer les pools de threads selon vos besoins:
   # Voir l'exemple de configuration ci-dessus

6. Tester les performances et ajuster la configuration si nécessaire

7. Optionnel: Utiliser le décorateur @thread_pool_executor pour automatiser
