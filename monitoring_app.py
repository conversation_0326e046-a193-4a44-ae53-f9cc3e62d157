#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Application FastAPI pour le monitoring du système RAG Confluence.
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

import uvicorn
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

app = FastAPI(
    title="Confluence RAG Monitoring",
    description="API de monitoring pour le système RAG Confluence",
    version="1.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modèles de données
class SyncStatus(BaseModel):
    status: str
    last_sync: Optional[datetime] = None
    items_processed: int = 0
    errors: int = 0
    warnings: int = 0

class SyncStats(BaseModel):
    total_content_items: int = 0
    total_attachments: int = 0
    processing_time_seconds: float = 0.0
    spaces_processed: List[str] = []

class CircuitBreakerStatus(BaseModel):
    service_name: str
    state: str
    failure_count: int = 0
    success_count: int = 0
    last_state_change: Optional[datetime] = None
    time_remaining: Optional[float] = None

# Routes API
@app.get("/", tags=["Général"])
async def root():
    """Point d'entrée principal de l'API."""
    return {"message": "Bienvenue sur l'API de monitoring du système RAG Confluence"}

@app.get("/circuit-breakers", tags=["Monitoring"], response_model=List[CircuitBreakerStatus])
async def get_circuit_breakers():
    """Récupère l'état de tous les Circuit Breakers."""
    # Cette fonction nécessite une référence à l'orchestrateur
    # qui sera injectée lors du démarrage de l'application
    if not hasattr(app.state, "orchestrator") or app.state.orchestrator is None:
        return []

    # Récupérer les statistiques des Circuit Breakers depuis le client Confluence
    client = app.state.orchestrator.client
    if not hasattr(client, "circuit_breaker"):
        return []

    circuit_breaker_stats = client.circuit_breaker.get_stats()

    # Convertir les statistiques en objets CircuitBreakerStatus
    result = []
    for service_name, stats in circuit_breaker_stats.get("services", {}).items():
        # Calculer le temps restant avant réinitialisation si le circuit est ouvert
        time_remaining = None
        if circuit_breaker_stats["state"] == "OPEN":
            last_change_time = datetime.fromisoformat(circuit_breaker_stats["last_state_change"])
            reset_timeout = client.config.circuit_breaker_config.reset_timeout
            elapsed = (datetime.now() - last_change_time).total_seconds()
            if elapsed < reset_timeout:
                time_remaining = reset_timeout - elapsed

        status = CircuitBreakerStatus(
            service_name=service_name,
            state=circuit_breaker_stats["state"],
            failure_count=stats.get("failure_count", 0),
            success_count=stats.get("success_count", 0),
            last_state_change=datetime.fromisoformat(circuit_breaker_stats["last_state_change"])
                if "last_state_change" in circuit_breaker_stats else None,
            time_remaining=time_remaining
        )
        result.append(status)

    return result

@app.get("/status", tags=["Monitoring"], response_model=SyncStatus)
async def get_status():
    """Récupère le statut actuel de la synchronisation."""
    # Ici, vous implémenteriez la logique pour récupérer le statut réel
    return SyncStatus(
        status="idle",
        last_sync=datetime.now(),
        items_processed=0,
        errors=0,
        warnings=0
    )

@app.get("/stats", tags=["Monitoring"], response_model=SyncStats)
async def get_stats():
    """Récupère les statistiques de synchronisation."""
    # Ici, vous implémenteriez la logique pour récupérer les statistiques réelles
    return SyncStats(
        total_content_items=0,
        total_attachments=0,
        processing_time_seconds=0.0,
        spaces_processed=[]
    )

@app.post("/trigger-sync", tags=["Actions"])
async def trigger_sync():
    """Déclenche une synchronisation manuelle."""
    # Ici, vous implémenteriez la logique pour déclencher une synchronisation
    return {"message": "Synchronisation déclenchée"}

if __name__ == "__main__":
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run("monitoring_app:app", host="0.0.0.0", port=port, reload=True)