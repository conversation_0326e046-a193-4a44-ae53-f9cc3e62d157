# API et HTTP
requests>=2.28.0
aiohttp>=3.8.3
atlassian-python-api>=3.32.0

# Traitement de données
pandas>=1.5.0
numpy>=1.23.0
pydantic>=2.0.0

# Traitement de texte et documents
python-docx>=0.8.11
PyPDF2>=3.0.1
openpyxl>=3.0.10
beautifulsoup4>=4.11.0
markdown>=3.4.0
markdownify>=0.11.6

# Stockage Cloud (optionnel)
# google-cloud-storage>=2.9.0

# Monitoring et API
fastapi>=0.88.0
uvicorn>=0.20.0

# Utilitaires
python-dotenv>=0.21.0
tqdm>=4.64.0
loguru>=0.6.0

# Tests
pytest>=7.2.0
pytest-asyncio>=0.20.0
pytest-cov>=4.0.0