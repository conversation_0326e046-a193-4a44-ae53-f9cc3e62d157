#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script principal pour lancer la synchronisation avec Confluence.
"""

import asyncio
import json
import logging
import os
from dotenv import load_dotenv

from confluence_rag.config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from confluence_rag.orchestrator import SyncOrchestrator
from confluence_rag.main import setup_logging

async def main():
    """Fonction principale asynchrone."""
    # Charger les variables d'environnement
    load_dotenv()

    # Configurer le logging
    setup_logging()

    # Charger la configuration
    config_params = {
        "url": os.getenv("CONFLUENCE_URL"),
        "default_space_key": os.getenv("DEFAULT_SPACE_KEY", "EXAMPLE")
    }

    # Ajouter les paramètres d'authentification selon la méthode disponible
    if os.getenv("CONFLUENCE_PAT_TOKEN"):
        # Méthode 1: Personal Access Token (PAT)
        config_params["pat_token"] = os.getenv("CONFLUENCE_PAT_TOKEN")
    elif os.getenv("CONFLUENCE_USERNAME") and os.getenv("CONFLUENCE_API_TOKEN"):
        # Méthode 2: API Token classique
        config_params["username"] = os.getenv("CONFLUENCE_USERNAME")
        config_params["api_token"] = os.getenv("CONFLUENCE_API_TOKEN")

    config = ConfluenceConfig(**config_params)

    # Charger les critères de recherche depuis le fichier (local ou GCS)
    criteria_file = os.getenv("CRITERIA_FILE_PATH", "criteres_recherche.json")
    gcs_bucket = os.getenv("CRITERIA_GCS_BUCKET")

    # Vérifier si le chemin du fichier est une URL GCS (gs://bucket/path)
    if criteria_file.startswith("gs://") and not gcs_bucket:
        # Extraire le bucket et le chemin de l'URL GCS
        gcs_path = criteria_file[5:]  # Supprimer le préfixe "gs://"
        bucket_end = gcs_path.find("/")
        if bucket_end > 0:
            gcs_bucket = gcs_path[:bucket_end]
            criteria_file = gcs_path[bucket_end+1:]
            logging.info(f"URL GCS détectée: bucket={gcs_bucket}, path={criteria_file}")

    # Charger les critères
    criteria = SearchCriteria.from_file(criteria_file, gcs_bucket)

    # Vérifier que les critères contiennent au moins un espace
    if not criteria.spaces:
        criteria.spaces = [config.default_space_key]
        logging.warning(f"Aucun espace spécifié dans les critères. Utilisation de l'espace par défaut: {config.default_space_key}")

    # Charger les configurations
    storage_config = StorageConfig.from_env()
    processing_config = ProcessingConfig.from_env()

    logging.info(f"Configuration de stockage: type={storage_config.storage_type}, "
                f"{'output_dir=' + storage_config.output_dir if storage_config.storage_type == 'filesystem' else 'bucket=' + storage_config.gcs_bucket_name}")

    logging.info(f"Configuration de traitement: max_parallel_downloads={processing_config.max_parallel_downloads}, "
                f"max_thread_workers={processing_config.max_thread_workers}, "
                f"chunk_size={processing_config.chunk_size}, "
                f"overlap_size={processing_config.overlap_size}")

    # Afficher un résumé des critères de recherche
    logging.info(f"Critères de recherche: {len(criteria.spaces)} espaces, "
                f"{len(criteria.labels)} étiquettes, "
                f"{len(criteria.types)} types de contenu, "
                f"max_results={criteria.max_results}, "
                f"include_attachments={criteria.include_attachments}, "
                f"include_children={criteria.include_children}")

    # Créer et exécuter l'orchestrateur
    orchestrator = SyncOrchestrator(
        config,
        criteria,
        storage_config,
        processing_config
    )
    try:
        result = await orchestrator.run()

        # Afficher un résumé des résultats
        logging.info(f"Synchronisation terminée avec succès en {result.get('processing_time_seconds', 0):.2f} secondes.")
        logging.info(f"Résultats: {result.get('total_content_items', 0)} contenus traités, "
                    f"{result.get('changed_content_items', 0)} contenus modifiés, "
                    f"{result.get('stored_content_items', 0)} contenus stockés, "
                    f"{result.get('stored_attachments', 0)} pièces jointes stockées.")

        # Enregistrer le rapport de synchronisation
        report_path = os.getenv("SYNC_REPORT_PATH", "last_sync_report.json")
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, default=str)
            logging.info(f"Rapport de synchronisation enregistré dans {report_path}")
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement du rapport de synchronisation: {e}")
    except Exception as e:
        logging.error(f"Erreur lors de la synchronisation: {e}")

if __name__ == "__main__":
    asyncio.run(main())